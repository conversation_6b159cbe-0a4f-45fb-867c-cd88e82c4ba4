# 计算方式选择页面优化说明

## 优化概述

对计算方式选择页面进行了重大优化，改为只显示选中的卡片，其他选项可通过展开/收起功能查看，提升了界面的简洁性和用户体验。

## 设计理念

### 1. 突出重点
- 选中的计算方式作为主要展示内容
- 使用特殊的卡片样式突出显示
- 清晰的"已选择"标识

### 2. 减少干扰
- 其他未选中的选项默认隐藏
- 避免过多选项造成的视觉混乱
- 专注于当前选择的方式

### 3. 保持完整性
- 提供展开功能查看其他选项
- 保持信息的完整性和透明度
- 用户可以了解所有可选方式

## 主要功能实现

### 1. 选中方式突出显示

#### 特殊卡片设计
```vue
<div class="selected-method-card" v-if="formData.calculationMethod">
  <div class="method-header">
    <el-icon class="method-icon">
      <component :is="getMethodIcon(formData.calculationMethod)" />
    </el-icon>
    <h4>{{ getCalculationMethodName(formData.calculationMethod) }}</h4>
    <div class="selected-badge">
      <el-icon><Check /></el-icon>
      <span>已选择</span>
    </div>
  </div>
  <!-- 方式描述和特性 -->
</div>
```

#### 视觉特色
- 蓝色边框和渐变背景
- 顶部彩色装饰条
- "已选择"绿色徽章
- 更大的图标和字体

### 2. 展开/收起功能

#### 切换按钮
```vue
<div class="expand-toggle">
  <el-button 
    type="text" 
    @click="toggleExpanded"
    class="toggle-button">
    <el-icon><component :is="isExpanded ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
    {{ isExpanded ? '收起其他选项' : '查看其他选项' }}
  </el-button>
</div>
```

#### 状态管理
```javascript
// 展开/收起状态
const isExpanded = ref(false);

// 切换展开/收起状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};
```

### 3. 其他选项展示

#### 条件渲染
```vue
<div class="other-methods-container" v-show="isExpanded">
  <div 
    class="calculation-method-card readonly collapsed" 
    v-if="formData.calculationMethod !== 'historical'">
    <!-- 历史价格法 -->
  </div>
  <!-- 其他方式... -->
</div>
```

#### 过滤逻辑
- 只显示未选中的计算方式
- 使用 `v-if` 条件渲染
- 避免重复显示选中的方式

## 技术实现细节

### 1. 动态图标组件

#### 图标映射方法
```javascript
const getMethodIcon = (method) => {
  const methodIcons = {
    'government': 'User',
    'cost': 'Document',
    'historical': 'Clock',
    'market': 'TrendCharts',
    'comprehensive': 'SetUp'
  };
  return methodIcons[method] || 'Setting';
};
```

#### 动态组件使用
```vue
<el-icon class="method-icon">
  <component :is="getMethodIcon(formData.calculationMethod)" />
</el-icon>
```

### 2. 内容数据管理

#### 描述信息方法
```javascript
const getMethodDescription = (method) => {
  const methodDescriptions = {
    'government': '基于政府或行业发布的指导价格进行计算的价格推荐',
    'cost': '基于产品成本结构进行分析计算的价格，更加客观可靠',
    // ... 其他描述
  };
  return methodDescriptions[method] || '';
};
```

#### 特性列表方法
```javascript
const getMethodFeatures = (method) => {
  const methodFeatures = {
    'government': [
      '符合政府规定，政策合规',
      '避免审计风险和合规性问题',
      '适用于受监管的产品或服务'
    ],
    // ... 其他特性
  };
  return methodFeatures[method] || [];
};
```

### 3. 样式设计系统

#### 选中卡片样式
```scss
.selected-method-card {
  border: 2px solid #409EFF;
  border-radius: 8px;
  padding: 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
  position: relative;
  overflow: hidden;
}

.selected-method-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409EFF 0%, #67c23a 100%);
}
```

#### 选择徽章样式
```scss
.selected-badge {
  display: flex;
  align-items: center;
  background: #67c23a;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}
```

#### 展开按钮样式
```scss
.toggle-button {
  color: #409EFF;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.toggle-button:hover {
  background-color: #f0f9ff;
  color: #1e40af;
}
```

## 用户体验提升

### 1. 视觉层次优化
- **主要信息**: 选中的计算方式突出显示
- **次要信息**: 其他选项收起，减少干扰
- **辅助信息**: 展开按钮提供查看入口

### 2. 交互体验改进
- **一目了然**: 用户立即看到选中的方式
- **按需查看**: 需要时可以展开查看其他选项
- **清晰反馈**: "已选择"徽章明确状态

### 3. 信息架构优化
- **重点突出**: 当前选择是最重要的信息
- **完整保留**: 所有选项信息都可以访问
- **逐步披露**: 渐进式信息展示

## 响应式设计

### 1. 移动端适配
```scss
@media (max-width: 768px) {
  .selected-method-card .method-features {
    grid-template-columns: 1fr;
  }
  
  .calculation-method-row {
    flex-direction: column;
  }
  
  .selected-method-card .method-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
```

### 2. 不同屏幕尺寸
- **大屏幕**: 网格布局，特性多列显示
- **中屏幕**: 保持基本布局结构
- **小屏幕**: 单列布局，垂直排列

## 性能优化

### 1. 条件渲染
- 使用 `v-if` 避免渲染未选中的方式
- 减少DOM节点数量
- 提高初始渲染性能

### 2. 懒加载展示
- 其他选项仅在展开时显示
- 减少初始页面复杂度
- 按需加载内容

### 3. 动画优化
- 平滑的展开/收起动画
- CSS transition 实现
- 硬件加速优化

## 可访问性考虑

### 1. 键盘导航
- 展开按钮支持键盘操作
- 清晰的焦点指示
- 逻辑的Tab顺序

### 2. 屏幕阅读器
- 语义化的HTML结构
- 适当的ARIA标签
- 状态变化的声明

### 3. 视觉对比度
- 足够的颜色对比度
- 清晰的文字可读性
- 明确的状态指示

## 总结

计算方式选择页面的优化实现了以下目标：

1. **简化界面**: 只显示选中的方式，减少视觉干扰
2. **突出重点**: 特殊设计的选中卡片，清晰的状态指示
3. **保持完整**: 展开功能确保所有信息可访问
4. **提升体验**: 更清晰的信息层次和交互反馈

这种设计既满足了简洁性的需求，又保持了信息的完整性，为用户提供了更好的选择体验。
