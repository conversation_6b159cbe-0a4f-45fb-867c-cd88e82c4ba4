<template>
  <div class="inquiry-container">
    <div class="inquiry-content">
    <!-- 询价流程导航 -->
    <div class="steps-container">
      <div class="inquiry-steps">
        <div class="step-item active">
          <div class="step-text">1. 发起询价</div>
        </div>
        <div class="step-arrow">&gt;</div>
        <div class="step-item">
          <div class="step-text">2. 导出询价函</div>
        </div>
        <div class="step-arrow">&gt;</div>
        <div class="step-item">
          <div class="step-text">3. 申请审批与报价</div>
        </div>
        <div class="step-arrow">&gt;</div>
        <div class="step-item">
          <div class="step-text">4. 上传报价回函</div>
        </div>
      </div>
    </div>

    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">询价列表</h2>
        <p class="page-desc">查看和管理您正在发起的询价</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="el-icon-plus" @click="handleStartInquiry">发起询价</el-button>
      </div>
    </div>
    <avue-crud :option="option"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <!-- 自定义项目名称列 -->
      <template #projectId="{row}">
        <span>{{ getProjectNameForDisplay(row.projectId) }}</span>
      </template>
      <!-- <template #menu-left>
        <el-button type="primary"
                   icon="el-icon-plus"
                   v-if="permission.inquiry_add"
                   @click="handleStartInquiry">发起询价
        </el-button>
      </template> -->

      <!-- 自定义操作列 -->
      <template #menu="{ row }">
        <div class="operation-buttons">
          <el-button type="text" size="small" class="action-btn" @click="handleView(row)">查看</el-button>
          <el-button type="text" size="small" class="action-btn" @click="handleExport(row)">导出询价函</el-button>
          <el-button type="text" size="small" class="action-btn" @click="handleUpload(row)">上传报价回函</el-button>
        </div>
      </template>
    </avue-crud>



    <!-- 发起询价对话框 -->
    <el-dialog
      title="发起询价"
      v-model="dialogVisible.inquiry"
      width="60%"
      :before-close="handleClose">
      <el-form :model="inquiryForm" ref="inquiryFormRef" label-width="120px" :rules="rules">
        <el-divider content-position="left">基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectId">
              <el-select
                v-model="inquiryForm.projectId"
                filterable
                remote
                reserve-keyword
                placeholder="请选择或输入项目名称"
                :remote-method="searchProjects"
                :loading="projectSearchLoading"
                @change="handleSelectProject"
                clearable
                style="width: 100%">
                <el-option
                  v-for="item in projectSearchResults"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                  <div class="project-option-item">
                    <div class="project-name">{{ item.name }}</div>
                    <div class="project-info" v-if="item.content">采购内容: {{ item.content }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称/型号" prop="productName">
              <el-input v-model="inquiryForm.productName" placeholder="请输入产品名称或型号"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="技术规格" prop="technicalSpec">
              <el-input v-model="inquiryForm.technicalSpec" placeholder="请输入技术规格"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="inquiryForm.quantity" :min="1" placeholder="请输入数量"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="质量标准" prop="qualityStandard">
              <el-input v-model="inquiryForm.qualityStandard" placeholder="请输入质量标准"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="包装要求" prop="packagingRequirements">
              <el-input v-model="inquiryForm.packagingRequirements" placeholder="请输入包装要求"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交货期" prop="deliveryPeriod">
              <el-input v-model="inquiryForm.deliveryPeriod" placeholder="请输入交货期"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务内容" prop="serviceContent">
              <el-input v-model="inquiryForm.serviceContent" placeholder="请输入服务内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务期限" prop="servicePeriod">
              <el-input v-model="inquiryForm.servicePeriod" placeholder="请输入服务期限"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务要求" prop="serviceRequirements">
              <el-input v-model="inquiryForm.serviceRequirements" placeholder="请输入服务要求"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="其他说明" prop="otherNotes">
              <el-input v-model="inquiryForm.otherNotes" placeholder="请输入其他说明"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间" prop="deadline">
              <el-date-picker
                v-model="inquiryForm.deadline"
                type="datetime"
                placeholder="选择截止时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible.inquiry = false">取 消</el-button>
          <el-button type="primary" @click="submitInquiry">提 交</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传报价回函对话框 -->
    <el-dialog
      title="上传报价回函"
      v-model="dialogVisible.upload"
      width="30%"
      :before-close="handleClose">
      <el-upload
        class="upload-demo"
        drag
        action="/xjzs/inquiry/upload-quotation"
        :headers="uploadHeaders"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">只能上传 xlsx/xls/pdf 文件，且不超过 10MB</div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible.upload = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 供应商列表对话框 -->
    <el-dialog
      title="已报价供应商列表"
      v-model="dialogVisible.supplier"
      width="80%"
      :before-close="handleClose">
      <el-table :data="supplierList" border style="width: 100%">
        <el-table-column prop="name" label="名称" width="120"></el-table-column>
        <el-table-column prop="content" label="技术参数或服务内容" width="200"></el-table-column>
        <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
        <el-table-column prop="supplier" label="询价对象" width="120"></el-table-column>
        <el-table-column prop="channel" label="询价渠道" width="120"></el-table-column>
        <el-table-column prop="unitPrice" label="单价" width="120"></el-table-column>
        <el-table-column prop="totalPrice" label="总价" width="120"></el-table-column>
        <el-table-column prop="remark" label="备注"></el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible.supplier = false">关 闭</el-button>
        </span>
      </template>
    </el-dialog>
    </div>
  </div>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/xjzs/projectInquiryRecord";
  import {exportInquiry} from "@/api/xjzs/inquiry";
  import {searchProjects, getProjectById} from "@/api/xjzs/search";
  import option from "@/option/xjzs/inquiry";
  import {mapGetters} from "vuex";
  import {getToken} from '@/utils/auth';
  import {formatDateNow} from "@/utils/date";
  import {downloadFile} from "@/utils/util";

  export default {
    name: "Inquiry",
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        dialogVisible: {
          inquiry: false,
          upload: false,
          supplier: false
        },
        inquiryForm: {
          projectId: '', // 项目 ID
          projectName: '', // 项目名称，用于显示和提交
          productName: '',
          technicalSpec: '',
          quantity: 1,
          qualityStandard: '',
          packagingRequirements: '',
          deliveryPeriod: '',
          serviceContent: '',
          servicePeriod: '',
          serviceRequirements: '',
          otherNotes: '',
          deadline: ''
        },
        rules: {
          projectId: [
            { required: true, message: '请选择项目', trigger: 'change' }
          ],
          deadline: [
            { required: true, message: '请选择截止时间', trigger: 'change' }
          ]
        },
        currentRow: null,
        supplierList: [],
        uploadHeaders: {
          'Blade-Auth': 'bearer ' + getToken()
        },
        // 项目搜索相关
        projectSearchLoading: false,
        projectSearchResults: [],
        projectSearchTimeout: null,
        // 项目缓存，用于存储项目ID和名称的映射关系
        projectCache: {}
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.inquiry_add, false),
          viewBtn: this.validData(this.permission.inquiry_view, false),
          delBtn: this.validData(this.permission.inquiry_delete, false),
          editBtn: this.validData(this.permission.inquiry_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {
      // 组件挂载时加载项目列表
      this.searchProjects('');
    },
    methods: {
      rowSave(row, done, loading) {
        // 将row数据转换为projectInquiryRecord格式
        const projectInquiryRecord = this.convertToProjectInquiryRecord(row);
        add(projectInquiryRecord).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }).catch(error => {
          loading();
          console.error('保存项目询价记录失败:', error);
        });
      },
      rowUpdate(row, _index, done, loading) {
        // 将row数据转换为projectInquiryRecord格式
        const projectInquiryRecord = this.convertToProjectInquiryRecord(row);
        update(projectInquiryRecord).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }).catch(error => {
          loading();
          console.error('更新项目询价记录失败:', error);
        });
      },
      // 将表单数据转换为projectInquiryRecord格式
      convertToProjectInquiryRecord(formData) {
        return {
          id: formData.id,
          projectId: formData.projectId, // 使用选择的项目 ID
          projectName: formData.projectName, // 保存项目名称
          productName: formData.productName,
          specifications: formData.technicalSpec || formData.specifications,
          quantity: formData.quantity,
          standard: formData.qualityStandard || formData.standard,
          packageRequirements: formData.packagingRequirements || formData.packageRequirements,
          deliveryDate: formData.deliveryPeriod || formData.deliveryDate,
          serviceContent: formData.serviceContent,
          servicePeriod: formData.servicePeriod,
          serviceRequirements: formData.serviceRequirements,
          otherInstructions: formData.otherNotes || formData.otherInstructions,
          deadline: formData.deadline
        };
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          })
          .catch(error => {
            console.error('删除项目询价记录失败:', error);
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          })
          .catch(error => {
            console.error('批量删除项目询价记录失败:', error);
          });
      },
      handleStartInquiry() {
        this.dialogVisible.inquiry = true;
      },
      handleView(row) {
        this.currentRow = row;
        this.loadSupplierList(row.id);
        this.dialogVisible.supplier = true;
      },
      handleExport(row) {
        this.$confirm("是否导出询价函?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          exportInquiry(row.id).then(res => {
            downloadFile(res.data, `询价函_${row.projectName}_${formatDateNow()}.xlsx`);
          });
        });
      },
      handleUpload(row) {
        this.currentRow = row;
        this.dialogVisible.upload = true;
      },
      handleClose(done) {
        done();
      },
      submitInquiry() {
        this.$refs.inquiryFormRef.validate((valid) => {
          if (valid) {
            // 将inquiryForm数据转换为projectInquiryRecord格式
            const projectInquiryRecord = this.convertToProjectInquiryRecord(this.inquiryForm);

            add(projectInquiryRecord).then(() => {
              this.dialogVisible.inquiry = false;
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "询价发起成功!"
              });
              // 重置表单
              this.$refs.inquiryFormRef.resetFields();
            }).catch(error => {
              console.error('提交项目询价记录失败:', error);
            });
          }
        });
      },
      beforeUpload(file) {
        const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                        file.type === 'application/vnd.ms-excel' ||
                        file.type === 'application/pdf';
        const isLt10M = file.size / 1024 / 1024 < 10;

        if (!isExcel) {
          this.$message.error('上传文件只能是 xlsx/xls/pdf 格式!');
          return false;
        }
        if (!isLt10M) {
          this.$message.error('上传文件大小不能超过 10MB!');
          return false;
        }
        return true;
      },
      handleUploadSuccess(_response, _file, _fileList) {
        this.$message.success('上传成功!');
        this.dialogVisible.upload = false;
        this.onLoad(this.page);
      },
      handleUploadError(err, _file, _fileList) {
        this.$message.error('上传失败: ' + err.message);
      },
      loadSupplierList(_inquiryId) {
        // 模拟数据，实际应该从后端获取
        this.supplierList = [
          {
            name: '物业服务',
            content: '提供保安，保洁进行物业服务一年',
            quantity: 1,
            supplier: '供应商A',
            channel: '供应商询价',
            unitPrice: '1,000,000',
            totalPrice: '1,000,000',
            remark: '无'
          },
          {
            name: '物业服务',
            content: '提供保安，保洁进行物业服务一年',
            quantity: 1,
            supplier: '供应商B',
            channel: '供应商询价',
            unitPrice: '1,230,000',
            totalPrice: '1,230,000',
            remark: '无'
          }
        ];
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      // 项目名称搜索方法
      searchProjects(query) {
        if (query !== '') {
          this.projectSearchLoading = true;
          // 清除之前的定时器
          if (this.projectSearchTimeout) {
            clearTimeout(this.projectSearchTimeout);
          }
          // 设置定时器，防止频繁请求
          this.projectSearchTimeout = setTimeout(() => {
            searchProjects(query).then(res => {
              this.projectSearchResults = res.data.data.records || [];
              this.projectSearchLoading = false;
            }).catch(error => {
              console.error('搜索项目失败:', error);
              this.projectSearchLoading = false;
            });
          }, 300);
        } else {
          // 如果查询条件为空，加载所有项目
          this.projectSearchLoading = true;
          searchProjects('').then(res => {
            this.projectSearchResults = res.data.data.records || [];
            this.projectSearchLoading = false;
          }).catch(error => {
            console.error('加载项目列表失败:', error);
            this.projectSearchLoading = false;
          });
        }
      },
      // 选择项目方法
      handleSelectProject(projectId) {
        if (projectId) {
          // 从搜索结果中找到选中的项目
          const selectedProject = this.projectSearchResults.find(item => item.id === projectId);
          if (selectedProject) {
            this.inquiryForm.projectName = selectedProject.name;
          }
        } else {
          // 清空选择时，同时清空项目名称
          this.inquiryForm.projectName = '';
        }
      },
      // 用于表格显示的项目名称
      getProjectNameForDisplay(projectId) {
        console.log(this.projectCache)
        if (!projectId) return '未知项目';

        // 如果缓存中已有该项目名称，直接返回
        if (this.projectCache[projectId]) {
          return this.projectCache[projectId];
        }

        // 如果缓存中没有，则调用获取方法并返回加载中的提示
        this.getProjectName(projectId);
        return '加载中...';
      },

      // 根据项目ID获取项目名称并缓存
      getProjectName(projectId) {
        // 如果缓存中已有该项目名称，直接返回
        if (this.projectCache[projectId]) {
          return this.projectCache[projectId];
        }

        // 如果缓存中没有，则调用API获取
        getProjectById(projectId).then(res => {
          if (res.data && res.data.data) {
            const projectName = res.data.data.name;
            // 将项目名称存入缓存
            this.projectCache[projectId] = projectName;
            // 强制更新视图
            this.$forceUpdate();
          }
        }).catch(error => {
          console.error('获取项目详情失败:', error);
          // 如果获取失败，则将项目ID作为名称存入缓存
          this.projectCache[projectId] = `项目 ID: ${projectId}`;
        });

        // 返回加载中的提示
        return '加载中...';
      },

      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, params).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;

          // 如果有数据，则遍历每条记录，获取项目名称
          if (this.data && this.data.length > 0) {
            this.data.forEach(item => {
              if (item.projectId && !item.projectName) {
                // 如果有projectId但没有projectName，则获取项目名称
                this.getProjectName(item.projectId);
              }
            });
          }

          this.loading = false;
          this.selectionClear();
        }).catch(error => {
          console.error('加载项目询价记录失败:', error);
          this.loading = false;
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
.page-header {
  margin: 10px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.header-right {
  margin-left: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.page-desc {
  font-size: 14px;
  color: #666;
}

.steps-container {
  background-color: #f8f9fb;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.inquiry-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-item {
  flex: 1;
  text-align: center;
}

.step-text {
  font-size: 14px;
  color: #606266;
}

.step-item.active .step-text {
  color: #409EFF;
  font-weight: 500;
}

.step-arrow {
  color: #c0c4cc;
  margin: 0 5px;
}



.mt-4 {
  margin-top: 16px;
}

.inquiry-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 20px;
}

.inquiry-content {
  width: 100%;
  max-width: 1200px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.full-width-table {
  width: 100%;
}

.full-width-table :deep(.el-table) {
  width: 100% !important;
}

.action-btn {
  color: #409EFF;
  margin: 0 5px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.project-option-item {
  width: 500px;
  padding: 5px 0;
}

.project-name {
  font-weight: bold;
  color: #409EFF;
}

.project-info {
  font-size: 12px;
  color: #999;
  margin-top: 3px;
}

/* 覆盖el-select的默认样式 */
:deep(.el-select-dropdown__item) {
  padding: 0 10px;
  height: auto;
  line-height: 1.5;
  padding-top: 8px;
  padding-bottom: 8px;
  width: 70% !important;
}

</style>
