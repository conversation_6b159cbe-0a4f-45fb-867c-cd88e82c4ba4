<template>
  <div class="training-fee-container">
    <div class="avue-crud">
      <el-row :hidden="!search" style="padding:6px 18px">
        <!-- 查询模块 -->
        <el-form :inline="true" :model="query" class="uniform-form" label-width="80">
          <!-- 添加查询条件 -->
          <el-form-item label="费用编码">
            <el-input v-model="query.feeCode" placeholder="请输入费用编码" clearable class="uniform-input"></el-input>
          </el-form-item>
          <el-form-item label="费用名称">
            <el-input v-model="query.feeName" placeholder="请输入费用名称" clearable class="uniform-input"></el-input>
          </el-form-item>
          <el-form-item label="费用类型">
            <el-select v-model="query.feeType" placeholder="请选择费用类型" clearable class="uniform-input">
              <el-option label="固定费用" value="fixed"></el-option>
              <el-option label="变动费用" value="variable"></el-option>
              <el-option label="其他费用" value="other"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="品牌">
            <el-input v-model="query.brand" placeholder="请输入品牌" clearable class="uniform-input"></el-input>
          </el-form-item>
          <el-form-item label="一级分类">
            <el-select v-model="query.firstCategory" placeholder="请选择一级分类" clearable class="uniform-input">
              <el-option
                v-for="item in categoryOptions.first"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="二级分类">
            <el-select v-model="query.secondCategory" placeholder="请选择二级分类" clearable class="uniform-input">
              <el-option
                v-for="item in categoryOptions.second"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="规格">
            <el-input v-model="query.specification" placeholder="请输入规格" clearable class="uniform-input"></el-input>
          </el-form-item>
          <el-form-item label="价格">
            <el-input-number v-model="query.price" placeholder="请输入价格" :precision="2" :step="0.1" :min="0" class="uniform-input" controls-position="right"></el-input-number>
          </el-form-item>

          <!-- 查询按钮 -->
          <el-form-item class="form-buttons">
            <el-button type="primary" icon="el-icon-search" @click="searchChange" class="uniform-button">搜 索</el-button>
            <el-button icon="el-icon-delete" @click="searchReset()" class="uniform-button">清 空</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="avue-crud__header">
          <!-- 头部左侧按钮模块 -->
          <div class="avue-crud__left">
            <el-button v-if="this.permissionList.addBtn" type="primary" icon="el-icon-plus" @click="handleAdd" class="uniform-button">新 增</el-button>
            <el-button v-if="this.permissionList.delBtn" type="danger" icon="el-icon-delete" @click="handleDelete" plain class="uniform-button">删 除</el-button>
            <!-- 工程咨询类时显示计算系数按钮 -->
            <el-button v-if="feeType === '工程咨询类'" type="primary" @click="showCoefficientDialog" class="uniform-button">计算系数</el-button>
            <!-- 京东慧采时显示Excel导入按钮和限价计算器按钮 -->
            <el-button v-if="feeType === '京东慧采'" type="success" icon="el-icon-upload2" @click="handleImportExcel" class="uniform-button">Excel导入</el-button>
            <!-- <el-button v-if="feeType === '京东慧采'" type="primary" icon="el-icon-s-tools" @click="openPriceCalculator" class="uniform-button">限价计算器</el-button> -->
          </div>
        </div>
      </el-row>
      <el-row>
        <!-- 列表模块 -->
        <el-table ref="table" v-loading="loading"
                  @selection-change="selectionChange"
                  :data="data"
                  :height="tableHeight"
                  style="width: 100%"
                  :border="option.border">
          <el-table-column type="selection" v-if="option.selection" width="55" align="center"></el-table-column>
          <el-table-column type="expand" v-if="option.expand" align="center"></el-table-column>
          <el-table-column v-if="option.index" label="#" type="index" width="50" align="center">
          </el-table-column>
          <template v-for="(item,index) in option.column">
            <!-- table字段 -->
            <el-table-column v-if="item.hide!==true"
                             :prop="item.prop"
                             :label="item.label"
                             :width="item.width"
                             :key="index">
            </el-table-column>
          </template>
          <!-- 操作栏模块 -->
          <el-table-column prop="menu" label="操作" :width="220" align="center">
            <template #="{row}">
              <el-button v-if="this.permissionList.viewBtn" type="primary" text icon="el-icon-view" @click="handleView(row)">查看</el-button>
              <el-button v-if="this.permissionList.editBtn" type="primary" text icon="el-icon-edit" @click="handleEdit(row)">编辑</el-button>
              <el-button v-if="this.permissionList.delBtn" type="primary" text icon="el-icon-delete" @click="rowDel(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <div class="avue-crud__pagination" style="width:100%">
          <!-- 分页模块 -->
          <el-pagination align="right"
                         background
                         @size-change="sizeChange"
                         @current-change="currentChange"
                         :current-page="page.currentPage"
                         :page-sizes="[10, 20, 30, 40, 50, 100]"
                         :page-size="page.pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="page.total">
          </el-pagination>
        </div>
      </el-row>
      <!-- 表单模块 -->
      <el-dialog :title="title"
                 v-model="box"
                 width="50%"
                 :before-close="beforeClose"
                 append-to-body>
        <el-form :disabled="view" ref="form" :model="form" label-width="100px" class="uniform-dialog-form">
          <!-- 表单字段 -->
          <el-form-item label="费用编码" prop="feeCode">
            <el-input v-model="form.feeCode" placeholder="请输入费用编码" class="uniform-dialog-input"/>
          </el-form-item>
          <el-form-item label="费用名称" prop="feeName">
            <el-input v-model="form.feeName" placeholder="请输入费用名称" class="uniform-dialog-input"/>
          </el-form-item>
          <el-form-item label="费用类型" prop="feeType">
            <el-select v-model="form.feeType" placeholder="请选择费用类型" class="uniform-dialog-input">
              <el-option label="固定费用" value="fixed"></el-option>
              <el-option label="变动费用" value="variable"></el-option>
              <el-option label="其他费用" value="other"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="品牌" prop="brand">
            <el-input v-model="form.brand" placeholder="请输入品牌" class="uniform-dialog-input"/>
          </el-form-item>
          <el-form-item label="一级分类" prop="firstCategory">
            <el-select v-model="form.firstCategory" placeholder="请选择一级分类" class="uniform-dialog-input">
              <el-option
                v-for="item in categoryOptions.first"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="二级分类" prop="secondCategory">
            <el-select v-model="form.secondCategory" placeholder="请选择二级分类" class="uniform-dialog-input">
              <el-option
                v-for="item in categoryOptions.second"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="规格" prop="specification">
            <el-input v-model="form.specification" placeholder="请输入规格" class="uniform-dialog-input"/>
          </el-form-item>
          <el-form-item label="价格" prop="price">
            <el-input-number v-model="form.price" placeholder="请输入价格" :precision="2" :step="0.1" :min="0" class="uniform-dialog-input" controls-position="right"/>
          </el-form-item>
        </el-form>
        <!-- 表单按钮 -->
        <template #footer>
          <span v-if="!view" class="dialog-footer">
            <el-button type="primary" icon="el-icon-circle-check" @click="handleSubmit" class="uniform-button">提 交</el-button>
            <el-button icon="el-icon-circle-close" @click="box = false" class="uniform-button">取 消</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 计算系数弹窗 -->
      <el-dialog
        title="计算系数"
        v-model="coefficientDialogVisible"
        width="80%"
        :before-close="handleCoefficientDialogClose"
        append-to-body
        destroy-on-close
      >
        <!-- 引入计算系数组件 -->
        <calculation-coefficient v-if="coefficientDialogVisible"></calculation-coefficient>
      </el-dialog>

      <!-- Excel导入对话框 -->
      <el-dialog
        title="Excel导入"
        v-model="excelBox"
        width="60%"
        :before-close="beforeExcelClose"
        append-to-body>
        <el-form ref="excelForm" :model="excelForm" label-width="100px" class="import-form">
          <el-form-item label="Excel文件" prop="excelFile">
            <el-upload
              class="excel-uploader"
              drag
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :limit="1"
              :on-exceed="handleExceed"
              :before-upload="beforeExcelUpload"
              :file-list="excelFileList"
              :http-request="() => {}"
              accept=".xlsx,.xls">
              <div class="upload-area">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip">只能上传xlsx/xls文件，且不超过10MB</div>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="数据覆盖" prop="isCovered">
            <el-switch
              v-model="excelForm.isCovered"
              :active-value="1"
              :inactive-value="0"
              active-text="是"
              inactive-text="否"
              class="cover-switch">
            </el-switch>
            <div class="switch-tip">选择"是"将覆盖已有数据，选择"否"将追加新数据</div>
          </el-form-item>
          <el-form-item label="模板下载">
            <el-button type="primary" @click="downloadTemplate" icon="el-icon-download" class="template-btn">下载模板</el-button>
          </el-form-item>

          <!-- 导入错误信息展示 -->
          <el-form-item v-if="importErrors.length > 0" label="错误信息">
            <div class="error-info-container">
              <div class="error-summary">
                导入结果: {{ importResult.message }}
              </div>
              <el-table :data="importErrors" border style="width: 100%" class="error-table">
                <el-table-column prop="rowNum" label="行号" width="80" align="center"></el-table-column>
                <el-table-column prop="columnName" label="列名" width="120"></el-table-column>
                <el-table-column prop="errorMessage" label="错误信息"></el-table-column>
                <el-table-column prop="cellContent" label="单元格内容" width="150"></el-table-column>
              </el-table>
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="excelBox = false" class="uniform-button">取 消</el-button>
            <el-button type="primary" @click="submitImport" :disabled="!excelForm.excelFile" class="uniform-button">导 入</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getList, getDetail, add, update, remove, importTrainingFee, downloadTemplate } from "@/api/xjzs/trainingFee";
import option from "@/option/xjzs/trainingFee";
import { mapGetters } from "vuex";
import CalculationCoefficient from './calculationCoefficient.vue';

export default {
  components: {
    CalculationCoefficient
  },
  props: {
    // 添加属性用于区分不同类型
    feeType: {
      type: String,
      default: '培训类'
    }
  },
  data () {
    return {
      height: 0,
      tableHeight: 400, // 默认表格高度
      // 弹框标题
      title: '',
      // 是否展示弹框
      box: false,
      // 是否显示查询
      search: true,
      // 加载中
      loading: true,
      // 是否为查看模式
      view: false,
      // 计算系数弹窗是否可见
      coefficientDialogVisible: false,
      // 查询信息
      query: {
        feeCode: '',
        feeName: '',
        feeType: '',
        brand: '',
        firstCategory: '',
        secondCategory: '',
        specification: '',
        price: 0
      },
      // 分类选项
      categoryOptions: {
        first: [
          { label: '技术培训', value: '技术培训' },
          { label: '管理培训', value: '管理培训' },
          { label: '专业培训', value: '专业培训' }
        ],
        second: [
          { label: '线上培训', value: '线上培训' },
          { label: '线下培训', value: '线下培训' },
          { label: '混合培训', value: '混合培训' }
        ]
      },
      // 分页信息
      page: {
        currentPage: 1,
        pageSize: 10,
        total: 40
      },
      // 表单数据
      form: {},
      // 选择行
      selectionList: [],
      // 表单配置
      option: option,
      // 表单列表
      data: [],
      // Excel导入相关数据
      excelBox: false,
      excelForm: {
        excelFile: null,
        isCovered: 0
      },
      excelFileList: [],
      importErrors: [],
      importResult: {
        success: false,
        successCount: 0,
        failCount: 0,
        message: ''
      }
    }
  },
  mounted() {
    this.init();
    this.onLoad(this.page);
    // 添加窗口大小变化监听，动态调整表格高度
    window.addEventListener('resize', this.resizeTable);
    this.resizeTable();
  },
  beforeUnmount() {
    // 组件销毁前移除监听
    window.removeEventListener('resize', this.resizeTable);
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.param_add, false),
        viewBtn: this.validData(this.permission.param_view, false),
        delBtn: this.validData(this.permission.param_delete, false),
        editBtn: this.validData(this.permission.param_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    // 将中文类型转换为后端需要的英文类型
    apiType() {
      const typeMap = {
        '培训类': 'training',
        '工程咨询类': 'consulting',
        '京东慧采': 'jdhc',
      };
      return typeMap[this.feeType] || 'training';
    }
  },
  methods: {
    init() {
      this.resizeTable();
      // 根据不同类型加载不同的分类选项
      this.loadCategoryOptions();
    },
    // 显示计算系数弹窗
    showCoefficientDialog() {
      this.coefficientDialogVisible = true;
    },
    // 打开限价计算器
    openPriceCalculator() {
      this.$router.push('/xjzs/price-calculator');
    },
    // 关闭计算系数弹窗
    handleCoefficientDialogClose(done) {
      this.coefficientDialogVisible = false;
      if (done) done();
    },
    // 加载分类选项
    loadCategoryOptions() {
      // 这里可以根据this.feeType加载不同的分类选项
      // 实际项目中可能需要从API获取
      if (this.feeType === '工程咨询类') {
        this.categoryOptions.first = [
          { label: '工程设计', value: '工程设计' },
          { label: '工程监理', value: '工程监理' },
          { label: '工程咨询', value: '工程咨询' }
        ];
        this.categoryOptions.second = [
          { label: '初步设计', value: '初步设计' },
          { label: '施工图设计', value: '施工图设计' },
          { label: '竣工图设计', value: '竣工图设计' }
        ];
      }
    },
    // 动态调整表格高度
    resizeTable() {
      // 根据查询条件的显示状态调整表格高度
      const queryHeight = this.search ? 180 : 0;
      this.tableHeight = window.innerHeight - 350 - queryHeight;
    },
    searchHide() {
      this.search = !this.search;
      this.$nextTick(() => {
        this.resizeTable();
      });
    },
    searchChange() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchReset() {
      this.query = {
        feeCode: '',
        feeName: '',
        feeType: '',
        brand: '',
        firstCategory: '',
        secondCategory: '',
        specification: '',
        price: ''
      };
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleSubmit() {
      if (!this.form.id) {
        add(this.form).then(() => {
          this.box = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
      } else {
        update(this.form).then(() => {
          this.box = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        })
      }
    },
    handleAdd() {
      this.title = '新增'
      this.form = {
        feeType: this.apiType // 设置默认类型
      }
      this.box = true
    },
    handleEdit(row) {
      this.title = '编辑'
      this.box = true
      getDetail(row.id).then(res => {
        this.form = res.data.data;
      });
    },
    handleView(row) {
      this.title = '查看'
      this.view = true;
      this.box = true;
      getDetail(row.id).then(res => {
        this.form = res.data.data;
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.selectionClear();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    beforeClose (done) {
      done()
      this.form = {};
      this.view = false;
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.table.clearSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      // 解构查询条件
      const {
        feeCode,
        feeName,
        feeType,
        brand,
        firstCategory,
        secondCategory,
        specification,
        price
      } = this.query;

      let values = {
        // 使用计算属性转换类型
        feeType: this.feeType,
        // 添加查询条件
        feeCode,
        feeName,
        feeTypeQuery: this.feeType, // 避免与类型参数冲突
        brand,
        firstCategory,
        secondCategory,
        specification,
        price
      };

      // 移除空值
      Object.keys(values).forEach(key => {
        if (values[key] === '' || values[key] === null || values[key] === undefined) {
          delete values[key];
        }
      });

      getList(page.currentPage, page.pageSize, values).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleImportExcel() {
      this.excelBox = true;
      this.excelFileList = [];
      this.excelForm = {
        excelFile: null,
        isCovered: 0
      };
      this.importErrors = [];
      this.importResult = {
        success: false,
        successCount: 0,
        failCount: 0,
        message: ''
      };
    },
    beforeExcelClose(done) {
      this.excelFileList = [];
      done();
    },
    handleExceed() {
      this.$message.warning('最多只能上传1个文件');
    },
    beforeExcelUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error('上传文件只能是 Excel 格式!');
        return false;
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },
    uploadExcelFile(params) {
      const loading = this.$loading({
        lock: true,
        text: '正在导入数据，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      importTrainingFee(params.file, this.excelForm.isCovered === 1)
        .then(res => {
          this.$message.success('导入成功');
          this.excelBox = false;
          this.onLoad(this.page); // 重新加载数据
        })
        .catch(error => {
          console.error('导入失败:', error);
          this.$message.error('导入失败: ' + (error.message || '未知错误'));
        })
        .finally(() => {
          loading.close();
        });
    },
    downloadTemplate() {
      downloadTemplate().then(res => {
        const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '计价标准导入模板.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);
      }).catch(error => {
        this.$message.error('下载模板失败: ' + (error.message || '未知错误'));
      });
    },
    // 文件选择变更
    handleFileChange(file) {
      if (file && file.raw) {
        // 直接设置文件对象
        this.excelForm.excelFile = file.raw;
        // 更新文件列表，确保UI显示正确
        this.excelFileList = [file];
      } else {
        this.excelForm.excelFile = null;
        this.excelFileList = [];
      }
      // 强制更新视图，确保按钮状态变化
      this.$forceUpdate();
    },
    // 提交导入
    submitImport() {
      if (!this.excelForm.excelFile) {
        this.$message.warning('请先选择Excel文件');
        return;
      }

      this.$confirm('确定要导入数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: '正在导入数据，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 确保文件对象正确传递
        const file = this.excelForm.excelFile;
        const isCovered = this.excelForm.isCovered === 1;

        console.log('导入文件:', file);
        console.log('是否覆盖:', isCovered);

        importTrainingFee(file, isCovered)
          .then(res => {
            // 检查导入结果
            const result = res.data;
            this.importResult = result;

            if (result.success && result.errors && result.errors.length === 0) {
              // 完全成功
              this.$message.success(result.message || '导入成功');
              this.excelBox = false; // 关闭弹窗
              this.onLoad(this.page); // 重新加载数据
            } else if (result.success && result.errors && result.errors.length > 0) {
              // 部分成功，显示错误信息
              this.$message.warning('部分数据导入成功，请查看错误信息');
              this.importErrors = result.errors;
            } else {
              // 完全失败
              this.$message.error(result.message || '导入失败');
              if (result.errors && result.errors.length > 0) {
                this.importErrors = result.errors;
              }
            }
          })
          .catch(error => {
            console.error('导入失败:', error);
            this.$message.error('导入失败: ' + (error.message || '未知错误'));
          })
          .finally(() => {
            loading.close();
          });
      }).catch(() => {
        // 用户取消导入
      });
    }
  }
};
</script>

<style scoped>
.training-fee-container {
  height: 100%;
  overflow: hidden;
}

/* 添加标题样式 */
.fee-type-title {
  margin-bottom: 15px;
  color: #409EFF;
  font-weight: 500;
}

/* 调整表格容器样式 */
.avue-crud {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.avue-crud .el-table {
  flex: 1;
}

.avue-crud__pagination {
  margin-top: 10px;
}

/* 统一输入框宽度 */
.uniform-input {
  width: 200px !important;
}



/* 对话框按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.dialog-footer .uniform-button {
  width: 100px;
}

/* Excel导入样式 */
.import-form {
  padding: 10px 20px;
}

.excel-uploader {
  width: 100%;
  margin-bottom: 10px;
}

.upload-area {
  padding: 20px 0;
}

.upload-area i {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 10px;
}

/* 错误信息样式 */
.error-info-container {
  margin-top: 10px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.error-summary {
  margin-bottom: 10px;
  font-weight: bold;
  color: #f56c6c;
}

.error-table {
  margin-top: 10px;
}

.error-table .el-table__row {
  background-color: #fff;
}

.el-upload__text {
  font-size: 16px;
  color: #606266;
  margin: 10px 0;
}

.el-upload__text em {
  color: #409EFF;
  font-style: normal;
  cursor: pointer;
}

.el-upload__tip {
  font-size: 13px;
  color: #909399;
}

.cover-switch {
  margin-right: 15px;
}

.switch-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.template-btn {
  width: 120px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.dialog-footer .uniform-button {
  width: 100px;
}

/* 美化上传区域 */
:deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  box-sizing: border-box;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409EFF;
}

:deep(.el-upload-list) {
  margin-top: 10px;
}

:deep(.el-upload-list__item) {
  transition: all 0.3s;
}

:deep(.el-upload-list__item:hover) {
  background-color: #f5f7fa;
}
</style>


















