import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/xjzs/project/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const getCompletedProjects = (current, size, params) => {
  return request({
    url: '/xjzs/project/completed-projects',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/xjzs/project/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/xjzs/project/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/xjzs/project/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/xjzs/project/submit',
    method: 'post',
    data: row
  })
}

export const importProject = (file, isCovered = false) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('isCovered', isCovered ? 1 : 0);
  return request({
    url: '/xjzs/project/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData,
    timeout: 3000000 // 300秒超时 
  })
}

// 获取所有项目（不分页）
export const getAllProjects = () => {
  return request({
    url: '/xjzs/project/all',
    method: 'get'
  })
}

