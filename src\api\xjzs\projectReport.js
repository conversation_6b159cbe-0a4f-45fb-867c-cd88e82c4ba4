import axios from 'axios';

export const checkReportExists = (projectId) => {
  return axios({
    url: '/xjzs/projectReport/checkExists',
    method: 'get',
    params: {
      projectId
    }
  });
};
export const checkReport = (projectId) => {
  return axios({
    url: '/xjzs/projectReport/checkReport',
    method: 'get',
    params: {
      projectId
    }
  });
};


export const saveProjectReport = (data) => {
  return axios({
    url: '/xjzs/projectReport/save',
    method: 'post',
    data
  });
};

export const getProjectReport = (projectId) => {
  return axios({
    url: '/xjzs/projectReport/detail',
    method: 'get',
    params: {
      projectId
    }
  });
};

export const exportProjectReport = (projectId) => {
  return axios({
    url: '/xjzs/projectReport/export',
    method: 'get',
    responseType: 'blob',
    params: {
      projectId
    }
  });
};

export const exportProjectReportPdf = (projectId,tableItems,tabledata) => {
  return axios({
    url: '/xjzs/projectReport/export-pdf',
    method: 'get',
    responseType: 'blob',
    params: {
      projectId,tableItems,tabledata
    }
  });
};