<template>
  <div class="step-content">
    <h3>引擎计算</h3>
    <p class="step-description">使用AI智能引擎计算项目最高限价</p>
    
    <div class="calculation-container">
      <div class="project-info">
        <h4>项目信息</h4>
        <div class="info-item">
          <span class="label">项目名称：</span>
          <span class="value">{{ formData.projectName }}</span>
        </div>
        <div class="info-item">
          <span class="label">项目类型：</span>
          <span class="value">{{ formData.projectType }}</span>
        </div>
        <div class="info-item">
          <span class="label">算法类别：</span>
          <span class="value">{{ formData.algorithmCategory }}</span>
        </div>
        <div class="info-item">
          <span class="label">计算方式：</span>
          <span class="value">{{ getCalculationMethodName(formData.calculationMethod) }}</span>
        </div>
      </div>
      
      <div class="calculation-process">
        <div v-if="!isCalculating && !calculationResult">
          <el-button type="primary" @click="startCalculation" :loading="isCalculating">
            开始计算
          </el-button>
          <p class="hint">点击按钮开始计算项目最高限价</p>
        </div>
        
        <div v-if="isCalculating" class="calculating">
          <el-progress type="circle" :percentage="calculationProgress" :status="calculationProgress === 100 ? 'success' : ''"></el-progress>
          <p class="progress-text">{{ calculationProgressText }}</p>
        </div>
        
        <div v-if="calculationResult" class="result">
          <div class="result-header">
            <h4>计算结果</h4>
            <el-tag type="success" v-if="calculationResult.success">计算成功</el-tag>
            <el-tag type="danger" v-else>计算失败</el-tag>
          </div>
          
          <div v-if="calculationResult.success" class="result-content">
            <div class="price-box">
              <div class="price-label">最高限价</div>
              <div class="price-value">¥ {{ formatPrice(calculationResult.totalCost) }}</div>
            </div>
            
            <!-- 计算过程展示 -->
            <div class="calculation-thinking" v-if="calculationResult.calculationProcess && calculationResult.calculationProcess.length > 0">
              <h5>计算思路</h5>
              <div class="thinking-console">
                <div v-for="(step, index) in calculationResult.calculationProcess" :key="index" class="console-line">
                  <span class="timestamp">[{{ formatTimestamp(index) }}]</span> {{ step }}
                </div>
                <div class="console-cursor">></div>
              </div>
            </div>
          </div>
          
          <div v-else class="error-message">
            <el-alert
              :title="calculationResult.errorMessage"
              type="error"
              :closable="false"
              show-icon>
            </el-alert>
          </div>
        </div>
      </div>
    </div>
    
    <div class="button-container">
      <el-button @click="prevStep">返回</el-button>
      <el-button type="primary" @click="nextStep" :disabled="!calculationResult || !calculationResult.success">
        下一步：生成报告
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { calculateAudit } from '@/api/xjzs/audit';

export default {
  name: 'EngineCalculationStep',
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['next-step', 'prev-step'],
  setup(props, { emit }) {
    const isCalculating = ref(false);
    const calculationProgress = ref(0);
    const calculationProgressText = ref('准备计算...');
    const calculationResult = ref(null);
    
    // 获取计算方式的中文名称
    const getCalculationMethodName = (method) => {
      const methodNames = {
        'government': '政府、行业指导价格法',
        'cost': '成本核算法',
        'historical': '历史价格法',
        'market': '市场调研法',
        'comprehensive': '综合定价法'
      };
      return methodNames[method] || '未选择';
    };
    
    // 格式化价格
    const formatPrice = (price) => {
      if (!price) return '0.00';
      return parseFloat(price).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };
    
    // 格式化时间戳
    const formatTimestamp = (index) => {
      // 模拟时间戳格式 HH:MM:SS
      const baseTime = new Date();
      baseTime.setHours(15);
      baseTime.setMinutes(37);
      baseTime.setSeconds(29 + index);
      
      const hours = baseTime.getHours().toString().padStart(2, '0');
      const minutes = baseTime.getMinutes().toString().padStart(2, '0');
      const seconds = baseTime.getSeconds().toString().padStart(2, '0');
      
      return `${hours}:${minutes}:${seconds}`;
    };
    
    // 准备计算参数
    const prepareCalculationParams = () => {
      const params = {
        serviceType: props.formData.algorithmCategory,
        projectId: props.formData.id || props.formData.selectedProject,
        projectName: props.formData.projectName,
        calculationMethod: props.formData.calculationMethod
      };
      
      // 根据不同的算法类型，准备不同的标准表数据
      if (props.formData.algorithmCategory === '培训类') {
        // 培训类标准表数据
        const trainingTableRows = props.formData.trainingTableRows || [];
        if (trainingTableRows.length > 0) {
          // 使用新的培训表格式
          params.trainingData = trainingTableRows.map(row => ({
            trainingLocation: row.trainingLocation || '线下',
            teacherTitle: row.teacherTitle || '副高级职称',
            hoursPerDay: row.hoursPerDay || 8,
            trainingDays: row.trainingDays || 1,
            trainingPeople: row.trainingPeople || 0
          }));
        }
        
        // 将标准表数据转为JSON字符串
        params.tableData = JSON.stringify(trainingTableRows);
      } else if (props.formData.algorithmCategory === '工程咨询类') {
        // 工程咨询类标准表数据
        const engineeringTableRows = props.formData.engineeringTableRows || [];
        params.tableData = JSON.stringify(engineeringTableRows);
      } else {
        params.serviceType='京东慧采'
        // 京东慧采标准表数据 - 使用standardTableRows作为默认标准表
        const standardTableRows = props.formData.jdTableRows ||props.formData.standardTableRows || [];
        params.tableData = JSON.stringify(standardTableRows);
      }
      
      return params;
    };

    // 开始计算
    const startCalculation = async () => {
      if (isCalculating.value) return;
      
      try {
        isCalculating.value = true;
        calculationProgress.value = 0;
        calculationProgressText.value = '准备计算...';
        calculationResult.value = null;
        
        // 模拟进度
        const progressInterval = setInterval(() => {
          if (calculationProgress.value < 95) {
            calculationProgress.value += Math.floor(Math.random() * 10) + 1;
            
            if (calculationProgress.value < 30) {
              calculationProgressText.value = '正在分析项目数据...';
            } else if (calculationProgress.value < 60) {
              calculationProgressText.value = '正在应用计算规则...';
            } else if (calculationProgress.value < 90) {
              calculationProgressText.value = '正在生成计算结果...';
            } else {
              calculationProgressText.value = '即将完成...';
            }
          }
        }, 300);
        
        // 准备计算参数
        const params = prepareCalculationParams();
        
        // 调用计算API
        const response = await calculateAudit(params);
        
        // 清除进度模拟
        clearInterval(progressInterval);
        
        // 设置进度为100%
        calculationProgress.value = 100;
        calculationProgressText.value = '计算完成';
        
        // 处理响应
        if (response.data && response.data.success) {
          calculationResult.value = {
            success: true,
            totalCost: response.data.data.totalCost,
            calculationProcess: response.data.data.calculationProcess || [],
            details: response.data.data.details || {}
          };
          
          // 保存计算结果到formData
          props.formData.calculationResult = calculationResult.value;
          
          ElMessage.success('计算成功');
        } else {
          calculationResult.value = {
            success: false,
            errorMessage: response.data?.msg || '计算失败，请检查输入数据'
          };
          ElMessage.error(calculationResult.value.errorMessage);
        }
      } catch (error) {
        calculationResult.value = {
          success: false,
          errorMessage: error.message || '计算过程中发生错误'
        };
        ElMessage.error(calculationResult.value.errorMessage);
      } finally {
        isCalculating.value = false;
      }
    };
    
    const nextStep = () => {
      if (!calculationResult.value || !calculationResult.value.success) {
        ElMessage.warning('请先完成计算');
        return;
      }
      
      emit('next-step');
    };
    
    const prevStep = () => {
      emit('prev-step');
    };
    
    return {
      isCalculating,
      calculationProgress,
      calculationProgressText,
      calculationResult,
      getCalculationMethodName,
      formatPrice,
      formatTimestamp,
      startCalculation,
      nextStep,
      prevStep
    };
  }
}
</script>

<style scoped>
.step-content {
  padding: 10px;
}

.step-content h3 {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.step-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 25px;
}

.calculation-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.project-info {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-info h4 {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-item .label {
  width: 100px;
  color: #606266;
}

.info-item .value {
  flex: 1;
  color: #303133;
  font-weight: 500;
}

.calculation-process {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calculating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
}

.result {
  width: 100%;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.result-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.price-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
}

.price-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.price-value {
  font-size: 24px;
  font-weight: bold;
  color: #67c23a;
}

.calculation-thinking {
  margin-top: 20px;
  width: 100%;
}

.calculation-thinking h5 {
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.thinking-console {
  background-color: #1e1e1e;
  color: #d4d4d4;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  position: relative;
}

.console-line {
  margin-bottom: 8px;
  word-break: break-word;
}

.timestamp {
  color: #569cd6;
  margin-right: 8px;
}

.console-cursor {
  color: #569cd6;
  font-weight: bold;
  animation: blink 1s infinite;
  position: absolute;
  bottom: 15px;
  left: 15px;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.calculation-steps {
  margin-top: 20px;
}

.calculation-steps h5 {
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  font-weight: bold;
}

.step-item .step-content {
  flex: 1;
  padding: 0;
}

.hint {
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.error-message {
  margin-top: 20px;
}
</style>








