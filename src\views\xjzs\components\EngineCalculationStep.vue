<template>
  <div class="step-content">
    <div class="step-header">
      <div class="step-icon">
        <el-icon size="24"><Cpu /></el-icon>
      </div>
      <div class="step-info">
        <h3>引擎计算</h3>
        <p class="step-description">使用AI智能引擎计算项目最高限价</p>
      </div>
    </div>
    
    <div class="calculation-container">
      <div class="project-info">
        <h4>项目信息</h4>
        <div class="info-item">
          <span class="label">项目名称：</span>
          <span class="value">{{ formData.projectName }}</span>
        </div>
        <div class="info-item">
          <span class="label">项目类型：</span>
          <span class="value">{{ formData.projectType }}</span>
        </div>
        <div class="info-item">
          <span class="label">算法类别：</span>
          <span class="value">{{ formData.algorithmCategory }}</span>
        </div>
        <div class="info-item">
          <span class="label">计算方式：</span>
          <span class="value">{{ getCalculationMethodName(formData.calculationMethod) }}</span>
        </div>
      </div>
      
      <div class="calculation-process">
        <!-- 自动计算状态显示 -->
        <div v-if="isCalculating" class="calculating">
          <div class="auto-calculation-header">
            <el-icon class="auto-icon"><Cpu /></el-icon>
            <h4>AI引擎正在自动计算...</h4>
          </div>
          <el-progress type="circle" :percentage="calculationProgress" :status="calculationProgress === 100 ? 'success' : ''"></el-progress>
          <p class="progress-text">{{ calculationProgressText }}</p>
        </div>

        <!-- 计算结果显示 -->
        <div v-if="calculationResult" class="result">
          <div class="result-header">
            <h4>计算结果</h4>
            <el-tag type="success" v-if="calculationResult.success">
              <el-icon><Check /></el-icon>
              计算成功
            </el-tag>
            <el-tag type="danger" v-else>
              <el-icon><Close /></el-icon>
              计算失败
            </el-tag>
          </div>

          <div v-if="calculationResult.success" class="result-content">
            <div class="price-box">
              <div class="price-label">最高限价</div>
              <div class="price-value">¥ {{ formatPrice(calculationResult.totalCost) }}</div>
              <div class="price-note">基于{{ getCalculationMethodName(formData.calculationMethod) }}计算</div>
            </div>

            <!-- 计算过程展示 -->
            <div class="calculation-thinking" v-if="showThinkingProcess">
              <h5>
                <el-icon><Cpu /></el-icon>
                AI计算思路
                <span class="thinking-status" v-if="isThinkingStreaming">
                  <el-icon class="streaming-icon"><Loading /></el-icon>
                  实时分析中...
                </span>
              </h5>
              <div class="thinking-console">
                <div class="console-header">
                  <div class="console-controls">
                    <span class="control-dot red"></span>
                    <span class="control-dot yellow"></span>
                    <span class="control-dot green"></span>
                  </div>
                  <div class="console-title">AI Engine Terminal</div>
                  <div class="console-info">{{ formatCurrentTime() }}</div>
                </div>
                <div class="console-content">
                  <div class="console-welcome">
                    <div class="ascii-art">
      ╔══════════════════════════════════════╗
      ║        AI 智能计算引擎 v2.0          ║
      ║     Intelligent Pricing Engine       ║
      ╚══════════════════════════════════════╝
                    </div>
                    <div class="system-info">
                      <span class="info-line">系统: 项目限价计算系统</span>
                      <span class="info-line">算法: {{ getCalculationMethodName(formData.calculationMethod) }}</span>
                      <span class="info-line">状态: {{ isThinkingStreaming ? '分析中' : '完成' }}</span>
                    </div>
                  </div>

                  <div v-for="(step, index) in displayedThinkingSteps" :key="index" class="console-line">
                    <span class="timestamp">[{{ formatTimestamp(index) }}]</span>
                    <span class="step-content">{{ step }}</span>
                  </div>

                  <div v-if="isThinkingStreaming" class="console-line typing">
                    <span class="typing-cursor">|</span>
                  </div>

                  <div v-if="!isThinkingStreaming && displayedThinkingSteps.length > 0" class="console-line complete">
                    <span class="complete-text">计算完成 ✓ 结果已生成</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="error-message">
            <el-alert
              :title="calculationResult.errorMessage"
              type="error"
              :closable="false"
              show-icon>
              <template #default>
                <p>{{ calculationResult.errorMessage }}</p>
                <el-button type="primary" size="small" @click="retryCalculation" style="margin-top: 10px;">
                  <el-icon><Refresh /></el-icon>
                  重新计算
                </el-button>
              </template>
            </el-alert>
          </div>
        </div>

        <!-- 初始状态（即将开始自动计算） -->
        <div v-if="!isCalculating && !calculationResult" class="auto-start-hint">
          <div class="auto-start-content">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <h4>准备自动计算</h4>
            <p>AI引擎正在准备计算项目最高限价...</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="button-container">
      <el-button @click="prevStep">返回</el-button>
      <el-button type="primary" @click="nextStep" :disabled="!calculationResult || !calculationResult.success">
        下一步：生成报告
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Cpu, Check, Close, Document, Refresh, Loading } from '@element-plus/icons-vue';
import { calculateAudit } from '@/api/xjzs/audit';

export default {
  name: 'EngineCalculationStep',
  components: {
    Cpu,
    Check,
    Close,
    Document,
    Refresh,
    Loading
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['next-step', 'prev-step'],
  setup(props, { emit }) {
    const isCalculating = ref(false);
    const calculationProgress = ref(0);
    const calculationProgressText = ref('准备计算...');
    const calculationResult = ref(null);
    const hasAutoStarted = ref(false);

    // 流式输出相关状态
    const displayedThinkingSteps = ref([]);
    const isThinkingStreaming = ref(false);
    const showThinkingProcess = ref(false);
    const streamingTimer = ref(null);
    
    // 获取计算方式的中文名称
    const getCalculationMethodName = (method) => {
      const methodNames = {
        'government': '政府、行业指导价格法',
        'cost': '成本核算法',
        'historical': '历史价格法',
        'market': '市场调研法',
        'comprehensive': '综合定价法'
      };
      return methodNames[method] || '未选择';
    };
    
    // 格式化价格
    const formatPrice = (price) => {
      if (!price) return '0.00';
      return parseFloat(price).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };
    
    // 格式化时间戳
    const formatTimestamp = (index) => {
      // 模拟时间戳格式 HH:MM:SS
      const baseTime = new Date();
      baseTime.setHours(15);
      baseTime.setMinutes(37);
      baseTime.setSeconds(29 + index * 2);

      const hours = baseTime.getHours().toString().padStart(2, '0');
      const minutes = baseTime.getMinutes().toString().padStart(2, '0');
      const seconds = baseTime.getSeconds().toString().padStart(2, '0');

      return `${hours}:${minutes}:${seconds}`;
    };

    // 格式化当前时间
    const formatCurrentTime = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };
    
    // 准备计算参数
    const prepareCalculationParams = () => {
      const params = {
        serviceType: props.formData.algorithmCategory,
        projectId: props.formData.id || props.formData.selectedProject,
        projectName: props.formData.projectName,
        calculationMethod: props.formData.calculationMethod
      };
      
      // 根据不同的算法类型，准备不同的标准表数据
      if (props.formData.algorithmCategory === '培训类') {
        // 培训类标准表数据
        const trainingTableRows = props.formData.trainingTableRows || [];
        if (trainingTableRows.length > 0) {
          // 使用新的培训表格式
          params.trainingData = trainingTableRows.map(row => ({
            trainingLocation: row.trainingLocation || '线下',
            teacherTitle: row.teacherTitle || '副高级职称',
            hoursPerDay: row.hoursPerDay || 8,
            trainingDays: row.trainingDays || 1,
            trainingPeople: row.trainingPeople || 0
          }));
        }
        
        // 将标准表数据转为JSON字符串
        params.tableData = JSON.stringify(trainingTableRows);
      } else if (props.formData.algorithmCategory === '工程咨询类') {
        // 工程咨询类标准表数据
        const engineeringTableRows = props.formData.engineeringTableRows || [];
        params.tableData = JSON.stringify(engineeringTableRows);
      } else {
        params.serviceType='京东慧采'
        // 京东慧采标准表数据 - 使用standardTableRows作为默认标准表
        const standardTableRows = props.formData.jdTableRows ||props.formData.standardTableRows || [];
        params.tableData = JSON.stringify(standardTableRows);
      }
      
      return params;
    };

    // 自动开始计算
    const autoStartCalculation = () => {
      if (hasAutoStarted.value || isCalculating.value || calculationResult.value) return;

      // 检查必要的数据是否存在
      if (!props.formData.calculationMethod) {
        ElMessage.warning('计算方式未设置，无法开始计算');
        return;
      }

      hasAutoStarted.value = true;
      // 延迟1秒开始计算，给用户一个准备的时间
      setTimeout(() => {
        startCalculation();
      }, 1000);
    };

    // 开始流式输出思路
    const startThinkingStream = (thinkingSteps) => {
      if (!thinkingSteps || thinkingSteps.length === 0) return;

      showThinkingProcess.value = true;
      isThinkingStreaming.value = true;
      displayedThinkingSteps.value = [];

      let currentIndex = 0;

      const streamNextStep = () => {
        if (currentIndex < thinkingSteps.length) {
          displayedThinkingSteps.value.push(thinkingSteps[currentIndex]);
          currentIndex++;

          // 随机延迟，模拟真实的思考过程
          const delay = Math.random() * 1000 + 500; // 500-1500ms
          streamingTimer.value = setTimeout(streamNextStep, delay);
        } else {
          isThinkingStreaming.value = false;
          if (streamingTimer.value) {
            clearTimeout(streamingTimer.value);
            streamingTimer.value = null;
          }
        }
      };

      // 开始流式输出
      streamNextStep();
    };

    // 停止流式输出
    const stopThinkingStream = () => {
      if (streamingTimer.value) {
        clearTimeout(streamingTimer.value);
        streamingTimer.value = null;
      }
      isThinkingStreaming.value = false;
    };

    // 重新计算
    const retryCalculation = () => {
      calculationResult.value = null;
      hasAutoStarted.value = false;
      showThinkingProcess.value = false;
      displayedThinkingSteps.value = [];
      stopThinkingStream();
      autoStartCalculation();
    };

    // 开始计算
    const startCalculation = async () => {
      if (isCalculating.value) return;
      
      try {
        isCalculating.value = true;
        calculationProgress.value = 0;
        calculationProgressText.value = '准备计算...';
        calculationResult.value = null;
        
        // 模拟进度
        const progressInterval = setInterval(() => {
          if (calculationProgress.value < 95) {
            calculationProgress.value += Math.floor(Math.random() * 10) + 1;
            
            if (calculationProgress.value < 30) {
              calculationProgressText.value = '正在分析项目数据...';
            } else if (calculationProgress.value < 60) {
              calculationProgressText.value = '正在应用计算规则...';
            } else if (calculationProgress.value < 90) {
              calculationProgressText.value = '正在生成计算结果...';
            } else {
              calculationProgressText.value = '即将完成...';
            }
          }
        }, 300);
        
        // 准备计算参数
        const params = prepareCalculationParams();
        
        // 调用计算API
        const response = await calculateAudit(params);
        
        // 清除进度模拟
        clearInterval(progressInterval);
        
        // 设置进度为100%
        calculationProgress.value = 100;
        calculationProgressText.value = '计算完成';
        
        // 处理响应
        if (response.data && response.data.success) {
          calculationResult.value = {
            success: true,
            totalCost: response.data.data.totalCost,
            calculationProcess: response.data.data.calculationProcess || [],
            details: response.data.data.details || {}
          };

          // 保存计算结果到formData
          props.formData.calculationResult = calculationResult.value;

          // 开始流式输出思路
          if (calculationResult.value.calculationProcess.length > 0) {
            startThinkingStream(calculationResult.value.calculationProcess);
          }

          ElMessage.success('计算成功');
        } else {
          calculationResult.value = {
            success: false,
            errorMessage: response.data?.msg || '计算失败，请检查输入数据'
          };
          ElMessage.error(calculationResult.value.errorMessage);
        }
      } catch (error) {
        calculationResult.value = {
          success: false,
          errorMessage: error.message || '计算过程中发生错误'
        };
        ElMessage.error(calculationResult.value.errorMessage);
      } finally {
        isCalculating.value = false;
      }
    };
    
    const nextStep = () => {
      if (!calculationResult.value || !calculationResult.value.success) {
        ElMessage.warning('请先完成计算');
        return;
      }
      
      emit('next-step');
    };
    
    const prevStep = () => {
      emit('prev-step');
    };

    // 组件挂载时自动开始计算
    onMounted(() => {
      autoStartCalculation();
    });

    // 监听计算方式变化，如果之前没有计算过，则自动开始计算
    watch(() => props.formData.calculationMethod, (newMethod) => {
      if (newMethod && !hasAutoStarted.value && !calculationResult.value) {
        autoStartCalculation();
      }
    });

    return {
      isCalculating,
      calculationProgress,
      calculationProgressText,
      calculationResult,
      displayedThinkingSteps,
      isThinkingStreaming,
      showThinkingProcess,
      getCalculationMethodName,
      formatPrice,
      formatTimestamp,
      formatCurrentTime,
      autoStartCalculation,
      retryCalculation,
      startCalculation,
      nextStep,
      prevStep
    };
  }
}
</script>

<style scoped>
.step-content {
  padding: 10px;
}

.step-content h3 {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.step-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 25px;
}

.calculation-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.project-info {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-info h4 {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-item .label {
  width: 100px;
  color: #606266;
}

.info-item .value {
  flex: 1;
  color: #303133;
  font-weight: 500;
}

.calculation-process {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 自动计算开始提示 */
.auto-start-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
}

.auto-start-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.loading-icon {
  font-size: 48px;
  color: #409EFF;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.auto-start-content h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.auto-start-content p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

/* 自动计算进行中 */
.calculating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.auto-calculation-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.auto-icon {
  font-size: 24px;
  color: #409EFF;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.auto-calculation-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.progress-text {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.result {
  width: 100%;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.result-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-header .el-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.price-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, #f0f9eb 0%, #e8f5e8 100%);
  border-radius: 12px;
  border: 2px solid #e1f3d8;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.1);
  position: relative;
  overflow: hidden;
}

.price-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #67c23a 0%, #85ce61 100%);
}

.price-label {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.price-value {
  font-size: 32px;
  font-weight: bold;
  color: #67c23a;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(103, 194, 58, 0.2);
}

.price-note {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.calculation-thinking {
  margin-top: 24px;
  width: 100%;
}

.calculation-thinking h5 {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.calculation-thinking h5 .el-icon {
  color: #ffffff;
  font-size: 20px;
}

.thinking-status {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 400;
  opacity: 0.9;
}

.streaming-icon {
  animation: rotate 1s linear infinite;
}

.thinking-console {
  background: linear-gradient(145deg, #0f172a 0%, #1e293b 100%);
  border: 1px solid #334155;
  border-radius: 12px;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
}

.console-header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #475569;
}

.console-controls {
  display: flex;
  gap: 8px;
}

.control-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;

  &.red { background: #ef4444; }
  &.yellow { background: #f59e0b; }
  &.green { background: #10b981; }
}

.console-title {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  flex: 1;
}

.console-info {
  color: #94a3b8;
  font-size: 12px;
  font-family: monospace;
}

.console-content {
  padding: 16px;
  background: #0f172a;
  color: #e2e8f0;
  font-size: 13px;
  line-height: 1.6;
  max-height: 400px;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #1e293b;
  }

  &::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 4px;

    &:hover {
      background: #64748b;
    }
  }
}

.ascii-art {
  color: #06b6d4;
  font-size: 11px;
  line-height: 1.2;
  margin-bottom: 16px;
  text-align: center;
  font-family: monospace;
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 20px;
  padding: 12px;
  background: rgba(6, 182, 212, 0.1);
  border-left: 3px solid #06b6d4;
  border-radius: 4px;
}

.info-line {
  color: #94a3b8;
  font-size: 12px;
}

.console-line {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  animation: fadeInUp 0.5s ease-out;
}

.line-prefix {
  color: #10b981;
  font-weight: 600;
  flex-shrink: 0;
}

.timestamp {
  color: #06b6d4;
  font-weight: 500;
  flex-shrink: 0;
}

.step-content {
  color: #e2e8f0;
  flex: 1;
}

.console-line.typing {
  animation: pulse 1s ease-in-out infinite;
}

.typing-cursor {
  color: #10b981;
  font-weight: bold;
  animation: blink 1s infinite;
}

.console-line.complete {
  color: #10b981;
  font-weight: 500;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #334155;
}

.complete-text {
  color: #10b981;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.calculation-steps {
  margin-top: 20px;
}

.calculation-steps h5 {
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  font-weight: bold;
}

.step-item .step-content {
  flex: 1;
  padding: 0;
}

.hint {
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.error-message {
  margin-top: 20px;
}
</style>








