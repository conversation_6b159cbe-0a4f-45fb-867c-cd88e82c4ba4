<template>
  <div v-if="isVisible" class="tour-guide-overlay">
    <!-- 遮罩层 -->
    <div class="tour-mask" @click="handleMaskClick"></div>
    
    <!-- 高亮区域 -->
    <div 
      class="tour-highlight" 
      :style="highlightStyle"
      v-if="currentStep && targetElement">
    </div>
    
    <!-- 引导卡片 -->
    <div 
      class="tour-card" 
      :style="cardStyle"
      v-if="currentStep">
      <div class="tour-card-content">
        <!-- 头部 -->
        <div class="tour-header">
          <div class="tour-title">{{ currentStep.title }}</div>
          <div class="tour-progress">{{ currentStepIndex + 1 }} / {{ steps.length }}</div>
        </div>
        
        <!-- 内容 -->
        <div class="tour-content">
          <p class="tour-description">{{ currentStep.description }}</p>
          <div v-if="currentStep.tips" class="tour-tips">
            <div v-for="(tip, index) in currentStep.tips" :key="index" class="tour-tip">
              <el-icon class="tip-icon"><Check /></el-icon>
              <span>{{ tip }}</span>
            </div>
          </div>
        </div>
        
        <!-- 底部操作 -->
        <div class="tour-footer">
          <div class="tour-actions">
            <el-button 
              v-if="currentStepIndex > 0" 
              @click="prevStep" 
              size="small">
              上一步
            </el-button>
            <el-button 
              v-if="currentStepIndex < steps.length - 1" 
              type="primary" 
              @click="nextStep" 
              size="small">
              下一步
            </el-button>
            <el-button 
              v-if="currentStepIndex === steps.length - 1" 
              type="primary" 
              @click="finish" 
              size="small">
              完成
            </el-button>
            <el-button 
              @click="skip" 
              size="small" 
              text>
              跳过引导
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 箭头指示器 -->
      <div class="tour-arrow" :class="arrowPosition"></div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { Check } from '@element-plus/icons-vue';

export default {
  name: 'TourGuide',
  components: {
    Check
  },
  props: {
    steps: {
      type: Array,
      required: true,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    },
    maskClosable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'finish', 'skip', 'step-change'],
  setup(props, { emit }) {
    const currentStepIndex = ref(0);
    const targetElement = ref(null);
    const highlightStyle = ref({});
    const cardStyle = ref({});
    const arrowPosition = ref('bottom');
    
    const isVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    });
    
    const currentStep = computed(() => {
      return props.steps[currentStepIndex.value] || null;
    });
    
    // 获取目标元素
    const getTargetElement = (selector) => {
      if (!selector) return null;
      
      // 支持多种选择器格式
      if (typeof selector === 'string') {
        return document.querySelector(selector);
      } else if (selector instanceof Element) {
        return selector;
      }
      return null;
    };
    
    // 计算高亮区域样式
    const calculateHighlightStyle = () => {
      if (!targetElement.value) return {};
      
      const rect = targetElement.value.getBoundingClientRect();
      const padding = 8;
      
      return {
        position: 'fixed',
        top: `${rect.top - padding}px`,
        left: `${rect.left - padding}px`,
        width: `${rect.width + padding * 2}px`,
        height: `${rect.height + padding * 2}px`,
        borderRadius: '8px',
        zIndex: 9999
      };
    };
    
    // 计算卡片位置
    const calculateCardPosition = () => {
      if (!targetElement.value) return {};
      
      const rect = targetElement.value.getBoundingClientRect();
      const cardWidth = 320;
      const cardHeight = 200; // 估算高度
      const spacing = 20;
      
      let top, left, arrow = 'bottom';
      
      // 优先在右侧显示
      if (rect.right + spacing + cardWidth <= window.innerWidth) {
        left = rect.right + spacing;
        top = rect.top + (rect.height - cardHeight) / 2;
        arrow = 'left';
      }
      // 其次在左侧显示
      else if (rect.left - spacing - cardWidth >= 0) {
        left = rect.left - spacing - cardWidth;
        top = rect.top + (rect.height - cardHeight) / 2;
        arrow = 'right';
      }
      // 在下方显示
      else if (rect.bottom + spacing + cardHeight <= window.innerHeight) {
        left = rect.left + (rect.width - cardWidth) / 2;
        top = rect.bottom + spacing;
        arrow = 'top';
      }
      // 在上方显示
      else {
        left = rect.left + (rect.width - cardWidth) / 2;
        top = rect.top - spacing - cardHeight;
        arrow = 'bottom';
      }
      
      // 确保卡片在视窗内
      left = Math.max(20, Math.min(left, window.innerWidth - cardWidth - 20));
      top = Math.max(20, Math.min(top, window.innerHeight - cardHeight - 20));
      
      arrowPosition.value = arrow;
      
      return {
        position: 'fixed',
        top: `${top}px`,
        left: `${left}px`,
        width: `${cardWidth}px`,
        zIndex: 10000
      };
    };
    
    // 更新位置
    const updatePosition = () => {
      if (!currentStep.value || !isVisible.value) return;
      
      nextTick(() => {
        targetElement.value = getTargetElement(currentStep.value.target);
        
        if (targetElement.value) {
          highlightStyle.value = calculateHighlightStyle();
          cardStyle.value = calculateCardPosition();
          
          // 滚动到目标元素
          targetElement.value.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      });
    };
    
    // 下一步
    const nextStep = () => {
      if (currentStepIndex.value < props.steps.length - 1) {
        currentStepIndex.value++;
        emit('step-change', currentStepIndex.value);
        updatePosition();
      }
    };
    
    // 上一步
    const prevStep = () => {
      if (currentStepIndex.value > 0) {
        currentStepIndex.value--;
        emit('step-change', currentStepIndex.value);
        updatePosition();
      }
    };
    
    // 完成引导
    const finish = () => {
      isVisible.value = false;
      emit('finish');
    };
    
    // 跳过引导
    const skip = () => {
      isVisible.value = false;
      emit('skip');
    };
    
    // 处理遮罩点击
    const handleMaskClick = () => {
      if (props.maskClosable) {
        skip();
      }
    };
    
    // 监听窗口大小变化
    const handleResize = () => {
      updatePosition();
    };
    
    // 监听步骤变化
    watch(() => currentStep.value, () => {
      updatePosition();
    });
    
    // 监听可见性变化
    watch(() => isVisible.value, (visible) => {
      if (visible) {
        currentStepIndex.value = 0;
        updatePosition();
      }
    });
    
    onMounted(() => {
      window.addEventListener('resize', handleResize);
      if (isVisible.value) {
        updatePosition();
      }
    });
    
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize);
    });
    
    return {
      isVisible,
      currentStep,
      currentStepIndex,
      targetElement,
      highlightStyle,
      cardStyle,
      arrowPosition,
      nextStep,
      prevStep,
      finish,
      skip,
      handleMaskClick
    };
  }
};
</script>

<style scoped>
.tour-guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  pointer-events: none;
}

.tour-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  pointer-events: auto;
}

.tour-highlight {
  background: transparent;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.3), 
              0 0 0 9999px rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

.tour-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  pointer-events: auto;
  position: relative;
  animation: tourCardIn 0.3s ease-out;
}

@keyframes tourCardIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.tour-card-content {
  padding: 20px;
}

.tour-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tour-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.tour-progress {
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
}

.tour-content {
  margin-bottom: 20px;
}

.tour-description {
  margin: 0 0 12px 0;
  color: #475569;
  line-height: 1.6;
  font-size: 14px;
}

.tour-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tour-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
}

.tip-icon {
  color: #10b981;
  font-size: 14px;
}

.tour-footer {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.tour-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 箭头样式 */
.tour-arrow {
  position: absolute;
  width: 0;
  height: 0;
}

.tour-arrow.top {
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

.tour-arrow.bottom {
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.tour-arrow.left {
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid white;
}

.tour-arrow.right {
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 8px solid white;
}
</style>
