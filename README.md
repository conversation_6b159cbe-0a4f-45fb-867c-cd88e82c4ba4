# 最高限价编审助手前端项目

## 项目简介

最高限价编审助手是一个专为采购限价编制设计的智能化系统，旨在简化采购限价编制流程，提高工作效率，让采购决策更加智能化。

## 主要功能

### 1. 发起询价
- **功能描述**: 使用标准化询价流程，从供应商库或外部询价，生成可追溯的电子记录
- **图标**: `/img/inquiry-icon.svg` (本地SVG图标)
- **路由**: `/xjzs/inquiry`
- **特点**: 标准化流程、可追溯记录

### 2. 智能编审
- **功能描述**: 流程化引导设计，提供多种计算方式，预设标准化选项，减少手动输入
- **图标**: `/img/review-icon.svg` (本地SVG图标)
- **路由**: `/xjzs/project-assistant`
- **特点**: 流程化引导、多种计算方式、标准化选项

### 3. 智能问答
- **功能描述**: 获取采购政策解读、法规咨询、历史案例参考，智能解答各类采购问题
- **图标**: `/img/qa-icon.svg` (本地SVG图标)
- **路由**: `/intelligent-qa`
- **特点**: 政策解读、法规咨询、案例参考

## 技术栈

- **前端框架**: Vue.js
- **UI组件库**: Element Plus
- **构建工具**: Vite
- **样式**: SCSS + Tailwind CSS
- **状态管理**: Vuex
- **路由**: Vue Router

## 项目结构

```
src/
├── api/                 # API接口
├── components/          # 公共组件
├── router/             # 路由配置
├── store/              # 状态管理
├── styles/             # 样式文件
├── utils/              # 工具函数
└── views/              # 页面组件
    └── wel/            # 欢迎页面
        └── index.vue   # 主页面

public/
└── img/                # 静态图片资源
    ├── inquiry-icon.svg    # 发起询价图标
    ├── review-icon.svg     # 智能编审图标
    └── qa-icon.svg         # 智能问答图标
```

## 最近更新

### ProjectAssistant 页面现代化优化 (2024-12)
- ✅ **现代化视觉设计**: 参考笔灵AI的清新界面风格，采用渐变背景和毛玻璃效果
- ✅ **优化的头部区域**: 新增图标、特性标签和更具吸引力的标题设计
- ✅ **简洁步骤导航**: 参考笔灵AI设计，左侧简洁透明导航，占用宽度更小
- ✅ **固定导航设计**: 左侧导航固定位置不随页面滚动，其他内容正常滚动
- ✅ **智能步骤管理**: 支持步骤间跳转，已完成步骤可重新访问
- ✅ **进度连接线**: 垂直连接线显示步骤进度，视觉更清晰
- ✅ **优化滚动体验**: 自定义滚动条样式，仅瀑布流内容可滚动
- ✅ **瀑布流式布局**: 右侧内容区域采用瀑布流卡片布局，信息组织更清晰
- ✅ **累积式内容展示**: 步骤内容从上到下累积显示，而非切换隐藏
- ✅ **简化信息架构**: 移除重复的操作提示，项目信息整合到主卡片中
- ✅ **统一步骤头部**: 每个步骤组件都有统一的图标和描述头部
- ✅ **增强的用户体验**: 添加页面过渡动画和丰富的状态指示
- ✅ **响应式布局**: 完全适配移动端和桌面端显示，移动端自动切换为顶部导航
- ✅ **信息卡片化**: 项目信息以卡片形式展示，更加直观
- ✅ **现代化按钮**: 渐变色按钮设计，增强交互反馈
- ✅ **优化的表格样式**: 更清晰的表格布局和添加按钮动画效果

#### 技术亮点：
- **简洁导航设计**: 参考笔灵AI的极简设计，180px宽度，背景透明，视觉清爽
- **固定导航系统**: 左侧导航使用 position: fixed 固定定位，不随页面滚动
- **瀑布流布局**: CSS Grid 实现的自适应瀑布流卡片布局
- **累积式显示**: v-show 实现的步骤内容累积展示，提升用户体验
- **智能步骤控制**: 基于状态的步骤导航，支持已完成步骤的重新访问
- **组件化设计**: 统一的步骤头部设计，保持视觉一致性
- **渐变背景**: 使用 CSS 渐变创建现代感背景
- **毛玻璃效果**: backdrop-filter 实现半透明毛玻璃效果
- **动画过渡**: Vue transition 组件实现页面切换动画
- **响应式网格**: CSS Grid 和 Flexbox 实现自适应布局
- **垂直进度条**: 创新的垂直进度显示，更直观的完成度反馈
- **交互反馈**: 悬停、点击状态的视觉反馈优化

### 添加行按钮优化 (2024)
- ✅ 将"添加行"文字按钮改为圆形+号图标按钮
- ✅ 统一所有表格的添加按钮样式和交互效果
- ✅ 添加悬停动画效果：缩放、阴影、颜色变化
- ✅ 优化按钮容器背景和边框样式
- ✅ 提升用户体验和视觉一致性

### 图标显示和布局优化完成 (2024)
- ✅ 将外部SVG图标链接替换为本地图标文件
- ✅ 修复了图标容器的大小和居中显示问题
- ✅ 统一所有图标容器的样式：64x64px (w-16 h-16) 固定大小
- ✅ 使用 `flex items-center justify-center` 实现图标完美居中
- ✅ 每个图标都有对应的主题色，保持视觉一致性
- ✅ 提高了页面加载速度和稳定性
- ✅ 避免了外部资源依赖问题

#### 技术解决方案：
- **布局问题**: 图标容器大小不固定，图标未居中显示，Tailwind CSS类不完整
- **解决方案**:
  - 创建自定义CSS类 `.icon-container` 和 `.icon-size`
  - 容器固定尺寸：64x64px，使用flexbox完美居中
  - 图标统一大小：28x28px
  - 避免依赖可能缺失的Tailwind CSS类
- **优势**: 所有图标容器大小一致，图标完美居中，样式完全可控

#### 修复详情：
1. **发起询价图标**: 蓝色主题 (#2563eb) - 对话框图标
2. **智能编审图标**: 绿色主题 (#059669) - 文档编辑图标
3. **智能问答图标**: 紫色主题 (#7c3aed) - 问号圆圈图标

## 开发说明

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

## 图标使用说明

项目中的功能图标已本地化，位于 `public/img/` 目录下：

1. **inquiry-icon.svg**: 发起询价功能图标，蓝色主题
2. **review-icon.svg**: 智能编审功能图标，绿色主题  
3. **qa-icon.svg**: 智能问答功能图标，紫色主题

所有图标都是SVG格式，支持响应式设计和颜色主题。

## 注意事项

- 图标文件路径使用绝对路径 `/img/` 开头
- 图标支持CSS类控制颜色和大小
- 建议保持图标的语义化命名规范
