export default {
  title: '',
  menuType: 'text',
  dialogDrag: false,
  dialogWidth: '80%',
  formWidth: 600,
  translate: false,
  searchMenuSpan: 6,
  searchShow: false,
  border: true,
  stripe: true,
  header:false,
  // refreshBtn: false,
  // columnBtn: false,
  // viewBtn: false,
  // menuBtn: false,
  // refreshBtn:false,
  // searchBtn: false,
  searchMenuPosition:'left',
  paginationAlign: 'center',
  menuWidth:400,

  gridBtn:false,
  column: [
    {
      label: '项目名称',
      prop: 'projectId',
      search: true,
      // width: 180,
      align: 'center',
      formatter: (row, column, cellValue, index) => {
        // 如果有projectName属性，直接返回
        if (row.projectName) {
          return row.projectName;
        }
        // 如果没有projectName属性，但有projectId，则调用组件方法获取
        if (row.projectId) {
          // 这里需要访问组件实例，但在配置文件中无法直接访问
          // 所以返回一个占位符，实际在组件中处理
          return `项目 ID: ${row.projectId}`;
        }
        return '未知项目';
      },
      rules: [{
        required: true,
        message: '请选择项目',
        trigger: 'change'
      }]
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      search: true,
      width: 100,
      align: 'center',
      dicData: [
        {
          label: '进行中',
          value: 1
        },
        {
          label: '已完成',
          value: 2
        }
      ]
    },
    {
      label: '截止日期',
      prop: 'deadline',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      width: 120,
      align: 'center',
      rules: [{
        required: true,
        message: '请选择截止日期',
        trigger: 'change'
      }]
    },

    {
      label: '更新时间',
      prop: 'updateTime',
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      addDisplay: false,
      editDisplay: false,
      hide: true
    },
    {
      label: '产品名称/型号',
      prop: 'productName',
      hide: true,
      rules: [{
        required: true,
        message: '请输入产品名称/型号',
        trigger: 'blur'
      }]
    },
    {
      label: '技术规格',
      prop: 'technicalSpec',
      hide: true
    },
    {
      label: '数量',
      prop: 'quantity',
      type: 'number',
      hide: true,
      rules: [{
        required: true,
        message: '请输入数量',
        trigger: 'blur'
      }]
    },
    {
      label: '质量标准',
      prop: 'qualityStandard',
      hide: true
    },
    {
      label: '包装要求',
      prop: 'packagingRequirements',
      hide: true
    },
    {
      label: '交货期',
      prop: 'deliveryPeriod',
      hide: true
    },
    {
      label: '服务内容',
      prop: 'serviceContent',
      hide: true
    },
    {
      label: '服务期限',
      prop: 'servicePeriod',
      hide: true
    },
    {
      label: '服务要求',
      prop: 'serviceRequirements',
      hide: true
    },
    {
      label: '其他说明',
      prop: 'otherNotes',
      hide: true
    }
  ]
};
