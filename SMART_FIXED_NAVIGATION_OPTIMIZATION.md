# 智能固定导航优化说明

## 优化概述

实现左侧步骤导航的智能固定定位，根据外层菜单的展开/收起状态动态调整位置，既保持导航始终可见，又避免遮挡外层菜单。

## 设计理念

### 1. 智能定位
- 根据外层菜单状态动态调整导航位置
- 菜单展开时：导航位于菜单右侧
- 菜单收起时：导航自动左移，节省空间

### 2. 固定可见性
- 使用 `position: fixed` 确保导航始终可见
- 不受页面滚动影响
- 提供稳定的操作参考点

### 3. 布局兼容性
- 与外层容器布局完美配合
- 不遮挡任何系统功能
- 响应式适配不同屏幕尺寸

## 技术实现

### 1. 基础固定定位

#### 导航基本定位
```scss
.steps-sidebar {
  width: 180px;
  position: fixed;
  left: 254px; /* 230px 菜单宽度 + 24px 间距 */
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
}
```

#### 定位计算说明
- 外层菜单展开宽度：230px（来自 `$sidebar_width`）
- 导航与菜单间距：24px
- 导航左边距：230px + 24px = 254px

### 2. 智能位置调整

#### 菜单收起状态适配
```scss
.avue--collapse .steps-sidebar {
  left: 84px; /* 60px 收起菜单宽度 + 24px 间距 */
}
```

#### 动态调整逻辑
- 检测外层容器的 `.avue--collapse` 类
- 菜单收起时：60px + 24px = 84px
- 自动左移：254px - 84px = 170px

### 3. 内容区域适配

#### 为导航留出空间
```scss
.content-area {
  margin-left: 204px; /* 180px 导航宽度 + 24px 间距 */
}

.avue--collapse .content-area {
  margin-left: 204px; /* 保持相同间距 */
}
```

#### 空间计算
- 导航宽度：180px
- 导航与内容间距：24px
- 内容左边距：180px + 24px = 204px

### 4. 垂直居中定位

#### 居中实现
```scss
.steps-sidebar {
  top: 50%;
  transform: translateY(-50%);
}
```

#### 居中优势
- 适应不同屏幕高度
- 视觉平衡更好
- 避免顶部或底部偏移

## 响应式设计

### 1. 桌面端 (>1024px)
- 固定定位导航
- 智能位置调整
- 完整功能展示

### 2. 平板端 (≤1024px)
```scss
@media (max-width: 1024px) {
  .steps-sidebar {
    position: static;
    left: auto;
    top: auto;
    transform: none;
    width: 100%;
  }
  
  .content-area {
    margin-left: 0;
  }
}
```

### 3. 移动端 (≤768px)
- 顶部水平导航
- 紧凑布局设计
- 触摸友好交互

## 外层菜单状态检测

### 1. CSS 类检测
```scss
/* 默认状态 - 菜单展开 */
.steps-sidebar {
  left: 254px;
}

/* 收起状态 - 菜单收起 */
.avue--collapse .steps-sidebar {
  left: 84px;
}
```

### 2. 状态切换
- 外层系统控制菜单展开/收起
- 通过添加/移除 `.avue--collapse` 类
- CSS 自动响应状态变化

### 3. 平滑过渡
```scss
.steps-sidebar {
  transition: left 0.3s ease;
}
```

## 布局层级管理

### 1. Z-index 设置
```scss
.steps-sidebar {
  z-index: 100;
}
```

### 2. 层级考虑
- 确保在内容之上
- 不遮挡模态框等高层级元素
- 与外层菜单层级协调

## 用户体验优势

### 1. 始终可见
- 导航固定在视窗中，不受滚动影响
- 用户随时可以查看当前步骤
- 快速切换到任意已完成步骤

### 2. 智能适应
- 自动适应菜单展开/收起状态
- 最大化利用屏幕空间
- 减少视觉干扰

### 3. 操作便利
- 稳定的操作参考点
- 清晰的步骤状态指示
- 直观的进度显示

## 技术优势

### 1. 性能优化
- 固定定位避免重排
- CSS 动画硬件加速
- 最小化 JavaScript 干预

### 2. 兼容性
- 现代浏览器全面支持
- 渐进式降级处理
- 响应式友好

### 3. 维护性
- 清晰的定位逻辑
- 易于调试和修改
- 模块化的样式结构

## 实现细节

### 1. 关键尺寸
```scss
/* 外层菜单尺寸（来自 variables.scss） */
$sidebar_width: 230px;      /* 展开状态 */
$sidebar_collapse: 60px;    /* 收起状态 */

/* 导航定位计算 */
展开时: 230px + 24px = 254px
收起时: 60px + 24px = 84px

/* 内容区域偏移 */
导航宽度: 180px + 24px = 204px
```

### 2. 动画效果
```scss
.steps-sidebar {
  transition: left 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}
```

### 3. 边界处理
```scss
.steps-sidebar {
  max-height: calc(100vh - 200px);
  overflow: visible;
}
```

## 总结

智能固定导航方案完美解决了以下问题：

1. **避免遮挡** - 根据菜单状态智能调整位置
2. **保持可见** - 固定定位确保始终可见
3. **空间优化** - 动态适应菜单展开/收起状态
4. **用户体验** - 提供稳定便利的导航操作

这种实现方式既满足了固定定位的需求，又完美兼容了外层容器的布局系统，为用户提供了最佳的操作体验。
