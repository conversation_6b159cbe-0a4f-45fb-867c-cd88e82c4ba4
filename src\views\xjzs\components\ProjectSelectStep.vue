<template>
  <div class="step-content">
    <div class="step-header">
      <div class="step-icon">
        <el-icon size="24"><Folder /></el-icon>
      </div>
      <div class="step-info">
        <h3>选择项目</h3>
        <p class="step-description">选择需要编制最高限价的项目，系统将自动加载项目信息</p>
      </div>
    </div>

    <div class="form-section">
      <div class="form-item">
        <label class="form-label">
          <el-icon><Document /></el-icon>
          项目名称
        </label>
        <el-select
          v-model="formData.selectedProject"
          placeholder="请选择项目"
          class="full-width modern-select"
          clearable
          filterable
          size="large">
          <el-option
            v-for="item in projectOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
    </div>

    <!-- 项目信息展示区域 -->
    <div class="project-info-section" v-if="formData.selectedProject">
      <div class="section-title">
        <el-icon><InfoFilled /></el-icon>
        项目信息
      </div>

      <div class="info-grid">
        <div class="info-card">
          <div class="info-label">项目编号</div>
          <div class="info-value">{{ formData.projectCode || '未设置' }}</div>
        </div>
        <div class="info-card">
          <div class="info-label">采购方式</div>
          <div class="info-value">{{ formData.procurementMethod || '未设置' }}</div>
        </div>
        <div class="info-card">
          <div class="info-label">项目类型</div>
          <div class="info-value">{{ formData.projectType || '未设置' }}</div>
        </div>
        <div class="info-card">
          <div class="info-label">算法类型</div>
          <div class="info-value">
            <el-tag :type="getAlgorithmTagType(formData.algorithmCategory)">
              {{ formData.algorithmCategory || '未设置' }}
            </el-tag>
          </div>
        </div>
      </div>

      <div class="form-item" v-if="formData.projectDescription">
        <label class="form-label">
          <el-icon><Reading /></el-icon>
          项目描述
        </label>
        <div class="description-card">
          {{ formData.projectDescription }}
        </div>
      </div>
    </div>

    <div class="standard-table-section" v-if="formData.selectedProject">
      <div class="section-title">
        <el-icon><Grid /></el-icon>
        标准表配置
      </div>
      <div class="table-description">
        根据项目算法类型自动匹配相应的标准表格式
      </div>

      <div class="standard-table-container">
        <!-- 根据算法类型动态显示不同的标准表 -->
        <div class="table-selector">
          <!-- 当算法类型为工程咨询类时显示工程咨询类标准表 -->
                <div v-if="formData.algorithmCategory === '工程咨询类'">
                  <h4 class="table-title">工程咨询类标准表</h4>
                  <div class="engineering-table">
                    <div class="table-header">
                      <div class="header-item">工程咨询类别</div>
                      <div class="header-item">编制完成工作成果</div>
                      <div class="header-item">编制技术难度</div>
                      <div class="header-item">工程人员配置情况</div>
                      <div class="header-item">评估值</div>
                      <div class="header-item">调整系数</div>
                      <div class="header-item">设施规模</div>
                      <div class="header-item">设计深度</div>
                      <div class="header-item">专业复杂系数</div>
                      <div class="header-item">工程复杂程度影响系数</div>
                      <div class="header-item">编制限额</div>
                    </div>
                    <div class="table-row">
                      <div class="row-item">
                        <el-select v-model="engineeringTable.category" placeholder="请选择类别" class="full-width">
                          <el-option label="建筑工程咨询类别" value="building"></el-option>
                          <el-option label="市政工程咨询类别" value="municipal"></el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.workResult" placeholder="请输入"></el-input>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.difficulty" placeholder="请输入"></el-input>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.staffing" placeholder="请输入"></el-input>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.evaluation" placeholder="请输入"></el-input>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.adjustmentFactor" placeholder="请输入"></el-input>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.facilityScale" placeholder="请输入"></el-input>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.designDepth" placeholder="请输入"></el-input>
                      </div>
                      <div class="row-item">
                        <el-select v-model="engineeringTable.complexityFactor" placeholder="请选择" class="full-width">
                          <el-option label="一般 (1.0)" value="1.0"></el-option>
                          <el-option label="复杂 (1.2)" value="1.2"></el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.engineeringComplexity" placeholder="请输入"></el-input>
                      </div>
                      <div class="row-item">
                        <el-input v-model="engineeringTable.budgetLimit" placeholder="请输入"></el-input>
                      </div>
                    </div>
                    <div class="add-row">
                      <el-button
                        type="primary"
                        icon="Plus"
                        circle
                        size="default"
                        @click="addEngineeringTableRow"
                        class="add-row-btn">
                      </el-button>
                    </div>
                  </div>
                </div>
                
                <!-- 当算法类型为培训类时显示培训类标准表 -->
                <div v-else-if="formData.algorithmCategory === '培训类'">
                  <h4 class="table-title">培训类标准表</h4>
                  <div class="standard-table">
                    <!-- 培训类标准表表头 -->
                    <div class="table-header">
                      <div class="header-item">费用名称</div> <!-- 新增费用名称列 -->
                      <div class="header-item">培训场景</div>
                      <div class="header-item">师资职称</div>
                      <div class="header-item">单位</div> <!-- 新增单位列 -->
                      <div class="header-item">每天学时</div>
                      <div class="header-item">培训天数</div>
                      <div class="header-item">培训人数</div>
                      <div class="header-item">操作</div>
                    </div>
                    
                    <!-- 培训类表格行 -->
                    <div v-for="(row, index) in trainingTableRows" :key="index" class="table-row">
                      <div class="row-item">
                        <el-select 
                          v-model="row.trainingLocation" 
                          placeholder="请选择培训场景" 
                          class="full-width">
                          <el-option label="线上" value="线上"></el-option>
                          <el-option label="线下" value="线下"></el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-select 
                          v-model="row.teacherTitle" 
                          placeholder="师资职称" 
                          class="full-width">
                          <el-option 
                            v-for="item in trainingCategories.teacherTitles" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-input-number v-model="row.hoursPerDay" :min="1" :max="24" placeholder="每天学时"></el-input-number>
                      </div>
                      <div class="row-item">
                        <el-input-number v-model="row.trainingDays" :min="1" placeholder="培训天数"></el-input-number>
                      </div>
                      <div class="row-item">
                        <el-input-number v-model="row.trainingPeople" :min="0" placeholder="培训人数"></el-input-number>
                      </div>
                      <div class="row-item">
                        <el-button 
                          type="danger" 
                          icon="Delete" 
                          circle 
                          size="small" 
                          @click="removeTrainingTableRow(index)"
                          v-if="trainingTableRows.length > 1">
                        </el-button>
                      </div>
                    </div>
                    
                    <div class="add-row">
                      <el-button
                        type="primary"
                        icon="Plus"
                        circle
                        size="default"
                        @click="addTrainingTableRow"
                        class="add-row-btn">
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 当算法类型为货物类、劳务类、信息类、办公类、综合类时显示京东慧采标准表 -->
                <div v-else>
                  <h4 class="table-title">京东慧采标准表</h4>
                  <div class="standard-table">
                    <div class="table-header">
                      <div class="header-item">费用名称</div>
                      <div class="header-item">一级分类</div>
                      <div class="header-item">二级分类</div>
                      <div class="header-item">数量</div>
                      <div class="header-item">单位</div> <!-- 新增单位列 -->
                      <div class="header-item">商品属性</div>
                      <div class="header-item">操作</div>
                    </div>
                    
                    <!-- 使用v-for循环渲染多行 -->
                    <div v-for="(row, index) in jdTableRows" :key="index" class="table-row">
                      <div class="row-item">
                        <el-select 
                          v-model="row.feeName" 
                          placeholder="请选择费用名称" 
                          class="full-width"
                          @change="handleFeeNameChange(row)">
                          <el-option 
                            v-for="item in jdCategories.feeNames" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-select 
                          v-model="row.firstCategory" 
                          placeholder="一级分类" 
                          class="full-width">
                          <el-option 
                            v-for="item in jdCategories.firstCategories" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-select 
                          v-model="row.secondCategory" 
                          placeholder="二级分类" 
                          class="full-width">
                          <el-option 
                            v-for="item in jdCategories.secondCategories" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <!-- 二级分类和商品属性之间添加单位选择框 -->
                      <div class="row-item">
                        <el-input-number v-model="row.quantity" :min="0" placeholder="请输入数量"></el-input-number>
                      </div>
                      <!-- 单位选择框 -->
                      <div class="row-item">
                        <el-select 
                          v-model="row.unit" 
                          placeholder="单位" 
                          class="full-width"
                          :disabled="!row.feeName"
                          @change="() => handleUnitChange(row)">  <!-- 添加 change 事件 -->
                          <el-option 
                            v-for="item in row.unitOptions" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-cascader
                          placeholder="请选择规格"
                          v-model="row.attributes"
                          :options="row.transformedAttributeData" 
                          :props="{
                            multiple: true
                          }"
                          collapse-tags
                          collapse-tags-tooltip
                          clearable
                          style="width: 100%"
                        ></el-cascader>
                      </div>

                      <div class="row-item">
                        <el-button 
                          type="danger" 
                          icon="Delete" 
                          circle 
                          size="small" 
                          @click="removeJdTableRow(index)"
                          v-if="jdTableRows.length > 1">
                        </el-button>
                      </div>
                    </div>
                    
                    <div class="add-row">
                      <el-button
                        type="primary"
                        icon="Plus"
                        circle
                        size="default"
                        @click="addJdTableRow"
                        class="add-row-btn">
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
    </div>

    <div class="button-container">
      <el-button @click="goBack" size="large" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <el-button type="primary" @click="nextStep" size="large" class="next-btn">
        下一步：选择计算方式
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Folder, Document, InfoFilled, Reading, Grid, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import { getAllProjects } from '@/api/xjzs/project';
import { getJdCategories, getProductAttributes, getTrainingCategories, getTrainingAttributes,getTrainingUnit } from '@/api/xjzs/trainingFee';

export default {
  name: 'ProjectSelectStep',
  components: {
    Plus,
    Folder,
    Document,
    InfoFilled,
    Reading,
    Grid,
    ArrowLeft,
    ArrowRight
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['next-step', 'prev-step'],
  setup(props, { emit }) {
    const projectOptions = ref([]);
    const activeTableType = ref('standard');
    
    // 标准表数据
    const standardTable = reactive({
      scene: '',
      firstCategory: '',
      secondCategory: '',
      productCode: '',
      quantity: 0
    });
    
    // 工程咨询类标准表数据
    const engineeringTable = reactive({
      category: '',
      workResult: '',
      difficulty: '',
      staffing: '',
      evaluation: '',
      adjustmentFactor: '',
      facilityScale: '',
      designDepth: '',
      complexityFactor: '1.0',
      engineeringComplexity: '',
      budgetLimit: '',
    });
    
    // 标准表行数据列表
    const standardTableRows = ref([]);
    const engineeringTableRows = ref([]);

    // 京东慧采分类数据
    const jdCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: []
    });
    
    // 京东慧采表格行数据
    const jdTableRows = ref([]); // 初始化为空数组
    
    // 培训类相关的数据和方法
    const trainingTableRows = ref([]);  
    const trainingCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: [],
      teacherTitles: []
    });

    // 获取项目列表
    const loadProjects = async () => {
      try {
        const res = await getAllProjects();
        if (res && res.data) {
          projectOptions.value = res.data.data.map(item => ({
            value: item.id,
            label: item.name,
            project: item
          }));
        }
      } catch (error) {
        console.error('加载项目列表失败:', error);
        ElMessage.error('加载项目列表失败');
      }
    };

    // 项目选择变更时更新表单数据
    const handleProjectChange = (projectId) => {
      if (!projectId) return;
      
      const selectedOption = projectOptions.value.find(p => p.value === projectId);
      if (selectedOption && selectedOption.project) {
        const project = selectedOption.project;
        
        // 将项目对象的属性赋值给formData
        props.formData.projectName = project.name;
        props.formData.projectCode = project.code || `P${project.id}`;
        props.formData.projectType = project.type || '';
        props.formData.projectCategory = project.category || '';
        props.formData.projectDescription = project.content || '';
        props.formData.procurementMethod = project.procurementMethod || '';
        props.formData.algorithmCategory = project.algorithmCategory || '';
        props.formData.id = project.id;
        
        // 根据项目类型自动切换标准表
        if (props.formData.projectType === '工程咨询') {
          activeTableType.value = 'engineering';
        } else {
          activeTableType.value = 'standard';
        }
      }
    };
    
    // 监听项目选择变化
    watch(() => props.formData.selectedProject, (newVal) => {
      if (newVal) {
        handleProjectChange(newVal);
      }
    });

    // 监听算法类型变化，初始化对应的表格
    watch(() => props.formData.algorithmCategory, (newVal) => {
      if (newVal === '培训类') {
        initTrainingTableRows();
        fetchTrainingCategories();
      } else if (newVal === '工程咨询类') {
        // 初始化工程咨询类表格
      } else {
        // 初始化京东慧采表格
        if (jdTableRows.value.length === 0) {
          addJdTableRow();
        }
        fetchJdCategories();
      }
    });

    // 标准表类型切换
    const handleTableTypeChange = (tabName) => {
      activeTableType.value = tabName;
    };
    
    // 添加标准表行
    const addStandardTableRow = () => {
      if (!standardTable.scene || !standardTable.firstCategory || !standardTable.productCode || standardTable.quantity <= 0) {
        ElMessage.warning('请完整填写标准表信息');
        return;
      }
      
      standardTableRows.value.push({
        ...JSON.parse(JSON.stringify(standardTable)),
        id: Date.now() // 临时ID
      });
      
      // 将标准表数据保存到formData中
      props.formData.standardTableRows = standardTableRows.value;
      
      // 清空当前行数据，准备下一行输入
      standardTable.scene = '';
      standardTable.firstCategory = '';
      standardTable.secondCategory = '';
      standardTable.productCode = '';
      standardTable.quantity = 0;
      
      ElMessage.success('添加成功');
    };
    
    // 添加工程咨询类标准表行
    const addEngineeringTableRow = () => {
      if (!engineeringTable.category || !engineeringTable.workResult) {
        ElMessage.warning('请至少填写类别和工作成果');
        return;
      }
      
      engineeringTableRows.value.push({
        ...JSON.parse(JSON.stringify(engineeringTable)),
        id: Date.now() // 临时ID
      });
      
      // 将工程咨询类标准表数据保存到formData中
      props.formData.engineeringTableRows = engineeringTableRows.value;
      
      // 清空部分字段，保留一些常用值
      engineeringTable.workResult = '';
      engineeringTable.difficulty = '';
      engineeringTable.staffing = '';
      engineeringTable.evaluation = '';
      engineeringTable.budgetLimit = '';
      
      ElMessage.success('添加成功');
    };

    // 获取京东慧采分类数据
    const fetchJdCategories = async () => {
      try {
        const res = await getJdCategories();
        if (res.data.success) {
          const data = res.data.data;
          jdCategories.feeNames = data.feeNames || [];
          jdCategories.firstCategories = data.firstCategories || [];
          jdCategories.secondCategories = data.secondCategories || [];
        }
      } catch (error) {
        console.error('获取京东慧采分类数据失败:', error);
      }
    };
    
    // 处理费用名称变更
    const handleFeeNameChange = async (row) => {
      if (!row.feeName) {
        row.attributeGroups = [];
        row.attributes = [];
        row.transformedAttributeData = []; // 清空当前行的属性数据
        row.firstCategory = ''; // 清空一级分类
        row.secondCategory = ''; // 清空二级分类
        row.unit = ''; // 清空单位
        row.unitOptions = []; // 清空单位选项
        return;
      }
      
      try {

        // 根据选择的费用名称获取相关数据
        const resUnit = await getTrainingUnit(row.feeName);
        if (resUnit.data.success) {
          // 自动填充相关字段
          const data = resUnit.data.data;
          if (data) {
            // 设置单位选项
            if (data) {
              row.unitOptions = data.map(item => ({
                label: item.unit,
                value: item.unit
              }));
              if (row.unitOptions.length > 0) {
                // 否则设置第一个单位为默认值
                row.unit = row.unitOptions[0].value;
              }
            } else {
              row.unitOptions = [];
              row.unit = '';
            }
          }
        }
        
        // 设置默认单位
        if (!row.unit && row.unitOptions.length > 0) {
          row.unit = row.unitOptions[0].value;
        }
        
       
      } catch (error) {
        console.error('获取商品属性失败:', error);
      }
    };

    // 处理单位变更
    const handleUnitChange = async (row) => {
      if (!row.feeName || !row.unit) {
        return;
      }    
      try {
        // 根据费用名称和单位获取属性
        const res = await getProductAttributes(row.feeName, row.unit);
        if (res.data.success) {
          const data = res.data.data || [];
          row.attributeGroups = data;
          
          // 更新当前行的商品属性数据格式
          row.transformedAttributeData = transformAttributeGroups(row.attributeGroups);
          console.log('单位变更后获取商品属性:', row.transformedAttributeData);
          row.attributes = []; // 清空已选属性
        }
      } catch (error) {
        console.error('获取商品属性失败:', error);
      }
    };


    
    // 添加京东慧采表格行
    // 添加京东慧采表格行
    const addJdTableRow = () => {
    jdTableRows.value.push({
    feeName: '',
    firstCategory: '',
    secondCategory: '',
    unit: '', // 添加单位字段
    unitOptions: [], // 添加单位选项
    attributes: [],
    attributeGroups: [],
    transformedAttributeData: [], // 为每一行添加独立的属性数据
    quantity: 1
    });
    };
    
    // 删除京东慧采表格行
    const removeJdTableRow = (index) => {
      jdTableRows.value.splice(index, 1);
    };

    // 初始化培训类表格行
    const initTrainingTableRows = () => {
      trainingTableRows.value = [{
        feeName: '', // 新增费用名称字段
        trainingLocation: '线下',
        teacherTitle: '',
        unit: '', // 新增单位字段
        unitOptions: [], // 新增单位选项数组
        hoursPerDay: 8,
        trainingDays: 1,
        trainingPeople: 0
      }];
    };

    // 添加培训类表格行
    const addTrainingTableRow = () => {
      trainingTableRows.value.push({
        feeName: '',
        trainingLocation: '线下',
        teacherTitle: '',
        unit: '',
        unitOptions: [],
        hoursPerDay: 8,
        trainingDays: 1,
        trainingPeople: 0
      });
    };

    // 删除培训类表格行
    const removeTrainingTableRow = (index) => {
      trainingTableRows.value.splice(index, 1);
    };
// 修改 transformAttributeGroups 方法，返回 Cascader 所需的格式
    const transformAttributeGroups = (attributeGroups) => {
      if (!attributeGroups || attributeGroups.length === 0) {
        return [];
      }
      
      const result = [];
      const groupedData = {};
      
      // 按 attributeName 分组
      attributeGroups.forEach(item => {
        const { attributeName, attributeValue } = item;
        if (!groupedData[attributeName]) {
          groupedData[attributeName] = new Set(); // 使用Set来避免重复值
        }
        groupedData[attributeName].add(attributeValue);
      });
      
      // 转换为 Cascader 所需的数组格式
      for (const key in groupedData) {
        const children = Array.from(groupedData[key]).map(value => ({
          value,
          label: value
        }));
        
        result.push({
          value: key,
          label: key,
          children
        });
      }
      
      return result;
    };
    // 获取培训类分类数据
    const fetchTrainingCategories = async () => {
      try {
        const res = await getTrainingCategories();
        if (res.data.success) {
          trainingCategories.feeNames = res.data.data.feeNames || [];
          trainingCategories.firstCategories = res.data.data.firstCategories || [];
          trainingCategories.secondCategories = res.data.data.secondCategories || [];
          trainingCategories.teacherTitles = res.data.data.teacherTitles || [];
          trainingCategories.units = res.data.data.units || []; // 新增单位数据
        }
      } catch (error) {
        console.error('获取培训类分类数据失败:', error);
      }
    };

    const nextStep = () => {
      // 验证是否选择了项目
      if (!props.formData.selectedProject) {
        ElMessage.warning('请先选择一个项目');
        return;
      }
      
      // 验证是否配置了标准表
      let hasStandardTable = false;
      
      if (props.formData.algorithmCategory === '培训类') {
        // 验证培训类标准表
        hasStandardTable = trainingTableRows.value.length > 0;
        if (hasStandardTable) {
          // 确保单位数据被保存
          trainingTableRows.value.forEach(row => {
            if (!row.unit && row.feeName) {
              // 如果没有设置单位但有费用名称，设置默认单位
              row.unit = '人天';
            }
          });
          // 保存培训类表格数据
          props.formData.trainingTableRows = trainingTableRows.value;
        }
      } else if (props.formData.algorithmCategory === '工程咨询类') {
        // 验证工程咨询类标准表
        hasStandardTable = engineeringTableRows.value.length > 0;
        if (hasStandardTable) {
          // 保存工程咨询类表格数据
          props.formData.engineeringTableRows = engineeringTableRows.value;
        }
      } else {
        // 验证京东慧采表格
        hasStandardTable = jdTableRows.value.length > 0 && jdTableRows.value.some(row => row.feeName);
        if (hasStandardTable) {
          // 只保存京东慧采表格数据的指定属性
          const filteredRows = jdTableRows.value.map(row => {
            // 确保单位数据存在
            if (!row.unit && row.feeName) {
              row.unit = '个';
            }
            
            // 只返回需要的属性
            return {
              feeName: row.feeName,
              firstCategory: row.firstCategory,
              secondCategory: row.secondCategory,
              quantity: row.quantity,
              unit: row.unit,
              attributes: row.attributes
            };
          });
          
          // 保存过滤后的京东慧采表格数据
          props.formData.jdTableRows = filteredRows;
        }
      }
      
      if (!hasStandardTable) {
        ElMessage.warning('请至少添加一条标准表数据');
        return;
      }
      
      emit('next-step');
    };

    const goBack = () => {
      history.back();
    };

    // 获取算法类型标签样式
    const getAlgorithmTagType = (algorithmCategory) => {
      const typeMap = {
        '培训类': 'success',
        '工程咨询类': 'warning',
        '货物类': 'info',
        '劳务类': 'primary',
        '信息类': 'danger',
        '办公类': '',
        '综合类': 'success'
      };
      return typeMap[algorithmCategory] || '';
    };

    onMounted(() => {
      loadProjects();
      // 根据当前算法类型初始化表格
      if (props.formData.algorithmCategory === '培训类') {
        initTrainingTableRows();
        fetchTrainingCategories();
      } else if (props.formData.algorithmCategory === '工程咨询类') {
        // 初始化工程咨询类表格
      } else {
        addJdTableRow();
        fetchJdCategories();
      }
    });

    return {
      projectOptions,
      activeTableType,
      standardTable,
      engineeringTable,
      standardTableRows,
      engineeringTableRows,
      handleTableTypeChange,
      addStandardTableRow,
      addEngineeringTableRow,
      nextStep,
      goBack,
      // 京东慧采相关
      jdCategories,
      jdTableRows,
      handleFeeNameChange,
      addJdTableRow,
      removeJdTableRow,
      fetchJdCategories,
      // 培训类相关
      trainingTableRows,
      trainingCategories,
      addTrainingTableRow,
      removeTrainingTableRow,
      initTrainingTableRows,
      fetchTrainingCategories,
      transformAttributeGroups,
      handleUnitChange,
      getAlgorithmTagType
    };
  }
}
</script>

<style scoped>
.step-content {
  padding: 0;
}

/* 步骤头部样式 */
.step-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;

  .step-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  .step-info {
    flex: 1;

    h3 {
      margin: 0 0 4px 0;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
    }

    .step-description {
      margin: 0;
      color: #64748b;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 32px;
}

.form-item {
  margin-bottom: 24px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.modern-select {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
    }

    &.is-focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}

/* 项目信息区域 */
.project-info-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.info-card {
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
  }

  .info-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .info-value {
    font-size: 14px;
    color: #1e293b;
    font-weight: 600;
  }
}

.description-card {
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #374151;
  line-height: 1.6;
  font-size: 14px;
}

/* 标准表区域 */
.standard-table-section {
  margin-bottom: 32px;
}

.table-description {
  color: #64748b;
  font-size: 14px;
  margin-bottom: 20px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
}

.full-width {
  width: 100%;
}

/* 标准表样式 */
.standard-table-container {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-selector {
  padding: 20px;
}

.table-header {
  display: flex;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 0;
  font-weight: 600;
  border-bottom: 1px solid #e2e8f0;
}

.header-item {
  flex: 1;
  padding: 0 12px;
  text-align: center;
  font-size: 13px;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-row {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8fafc;
  }

  &:last-child {
    border-bottom: none;
  }
}

.row-item {
  flex: 1;
  padding: 0 8px;
}

.add-row {
  padding: 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 2px dashed #cbd5e1;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-top-color: #667eea;
  }
}

.add-row-btn {
  width: 48px;
  height: 48px;
  border: 2px dashed #667eea;
  background: transparent;
  color: #667eea;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: none;

  &:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    transform: scale(1.05) translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  &:focus {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  }
}

/* 工程咨询类标准表样式 */
.engineering-table .table-header,
.engineering-table .table-row {
  display: grid;
  grid-template-columns: repeat(11, 1fr);
  gap: 5px;
}

.engineering-table .header-item,
.engineering-table .row-item {
  padding: 5px;
  text-align: center;
  font-size: 12px;
}

/* 按钮容器样式 */
.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
  gap: 16px;
}

.back-btn {
  min-width: 120px;
  border: 2px solid #e5e7eb;
  color: #6b7280;
  background: white;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #9ca3af;
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.next-btn {
  min-width: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .step-header {
    flex-direction: column;
    text-align: center;
    padding: 20px;

    .step-info h3 {
      font-size: 18px;
    }
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .button-container {
    flex-direction: column;
    gap: 12px;

    .back-btn,
    .next-btn {
      width: 100%;
      min-width: auto;
    }
  }

  .table-header,
  .table-row {
    font-size: 12px;
  }

  .header-item,
  .row-item {
    padding: 0 4px;
  }
}
</style>



