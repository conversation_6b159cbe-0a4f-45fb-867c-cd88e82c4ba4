<template>
  <basic-container>
    <div class="project-assistant-container">
      <!-- 优化后的头部区域 -->
      <div class="assistant-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon size="32"><Document /></el-icon>
          </div>
          <div class="header-text">
            <h1 class="main-title">AI智能编审助手</h1>
            <p class="subtitle">3分钟轻松搞定项目最高限价编制</p>
            <div class="feature-tags">
              <span class="tag">智能分析</span>
              <span class="tag">快速生成</span>
              <span class="tag">标准格式</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 - 左右布局 -->
      <div class="main-content-layout">
        <!-- 左侧步骤导航 -->
        <div class="steps-sidebar">
          <div class="sidebar-header">
            <h3>编制流程</h3>
            <div class="progress-info">
              <span class="current-step">第 {{ activeStep }} 步</span>
              <span class="total-steps">共 4 步</span>
            </div>
          </div>

          <div class="steps-navigation">
            <div
              v-for="(step, index) in steps"
              :key="index"
              class="step-nav-item"
              :class="{
                'active': activeStep === index + 1,
                'completed': activeStep > index + 1,
                'disabled': activeStep < index + 1
              }"
              @click="navigateToStep(index + 1)">

              <div class="step-nav-icon">
                <el-icon v-if="activeStep > index + 1" class="completed-icon">
                  <Check />
                </el-icon>
                <el-icon v-else-if="activeStep === index + 1" class="active-icon">
                  <component :is="step.icon" />
                </el-icon>
                <span v-else class="step-number">{{ index + 1 }}</span>
              </div>

              <div class="step-nav-content">
                <div class="step-nav-title">{{ step.title }}</div>
                <div class="step-nav-description">{{ step.description }}</div>
              </div>

              <div class="step-nav-status">
                <el-icon v-if="activeStep > index + 1" class="status-icon completed">
                  <CircleCheckFilled />
                </el-icon>
                <el-icon v-else-if="activeStep === index + 1" class="status-icon active">
                  <Loading />
                </el-icon>
                <div v-else class="status-dot pending"></div>
              </div>
            </div>
          </div>

          <!-- 左侧进度条 -->
          <div class="sidebar-progress">
            <div class="progress-label">整体进度</div>
            <div class="progress-bar-container">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ height: `${(activeStep / 4) * 100}%` }"></div>
              </div>
              <span class="progress-percentage">{{ Math.round((activeStep / 4) * 100) }}%</span>
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="content-area">
          <div class="step-content-container">
            <transition name="slide-fade" mode="out-in">
              <!-- 步骤1：选择项目 -->
              <div v-if="activeStep === 1" key="step1" class="step-wrapper">
                <ProjectSelectStep
                  :formData="formData"
                  @next-step="nextStep"
                  @prev-step="prevStep" />
              </div>

              <!-- 步骤2：选择计算方式 -->
              <div v-else-if="activeStep === 2" key="step2" class="step-wrapper">
                <CalculationMethodStep
                  :formData="formData"
                  @next-step="nextStep"
                  @prev-step="prevStep"
                />
              </div>

              <!-- 步骤3：引擎计算 -->
              <div v-else-if="activeStep === 3" key="step3" class="step-wrapper">
                <EngineCalculationStep
                  :formData="formData"
                  @next-step="nextStep"
                  @prev-step="prevStep"
                />
              </div>

              <!-- 步骤4：生成报告 -->
              <div v-else-if="activeStep === 4" key="step4" class="step-wrapper">
                <GenerateReportStep
                  :formData="formData"
                  :isDetailMode="isDetailMode"
                  @prev-step="prevStep"
                  @finish="finishProcess"
                />
              </div>
            </transition>
          </div>
        </div>
      </div>


    </div>
  </basic-container>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router'; // 添加导入
import { ElMessage } from 'element-plus';
import { UploadFilled, Download, Plus as PlusIcon, Check, Clock, TrendCharts, Document, User, SetUp, Folder, Setting, Cpu, DocumentCopy, CircleCheckFilled, Loading } from '@element-plus/icons-vue';
import ProjectSelectStep from './components/ProjectSelectStep.vue';
import CalculationMethodStep from './components/CalculationMethodStep.vue';
import EngineCalculationStep from './components/EngineCalculationStep.vue';
import GenerateReportStep from './components/GenerateReportStep.vue';
import { getJdCategories, getProductAttributes, getTrainingCategories, getTrainingAttributes } from '@/api/xjzs/trainingFee';
import { getDetail } from '@/api/xjzs/project'; // 添加导入
import { getProjectReport } from '@/api/xjzs/projectReport'; // 添加导入
import { Plus, Delete } from '@element-plus/icons-vue';

export default {
  name: 'ProjectAssistant',
  components: {
    Check,
    Clock,
    TrendCharts,
    Document,
    User,
    SetUp,
    Folder,
    Setting,
    Cpu,
    DocumentCopy,
    CircleCheckFilled,
    Loading,
    ProjectSelectStep,
    CalculationMethodStep,
    EngineCalculationStep,
    GenerateReportStep
  },
  setup() {
    const activeStep = ref(1);
    const route = useRoute();
    const isDetailMode = ref(false); // 添加详情模式标志

    // 步骤配置数据
    const steps = ref([
      {
        title: '选择项目',
        description: '配置项目基础信息',
        icon: 'Folder'
      },
      {
        title: '选择计算方式',
        description: '智能推荐计算方法',
        icon: 'Setting'
      },
      {
        title: '引擎计算',
        description: 'AI智能分析计算',
        icon: 'Cpu'
      },
      {
        title: '生成报告',
        description: '输出专业报告',
        icon: 'DocumentCopy'
      }
    ]);
    
    const formData = reactive({
      selectedProject: '',
      projectName: '',
      id: '',
      projectType: '',
      projectCategory: '',
      projectDescription: '',
      projectFiles: [],
      procurementMethod: '',
      algorithmCategory: '',
      calculationMethod: '',
      // 初始化标准表数据数组
      standardTableRows: [],
      engineeringTableRows: [],
      trainingTableRows: [],
      // 添加报告数据字段
      calculationResult: null,
      reportData: null
    });



    // 项目选择变更时更新表单数据
    const handleProjectChange = (projectId) => {
      if (!projectId) return;
      
      const selectedOption = projectOptions.value.find(p => p.value === projectId);
      if (selectedOption && selectedOption.project) {
        const project = selectedOption.project;
        
        // 将项目对象的属性赋值给formData
        formData.projectName = project.name;
        formData.projectCode = project.code || `P${project.id}`;
        formData.projectType = project.type || '';
        formData.projectCategory = project.category || '';
        formData.projectDescription = project.content || '';
        formData.procurementMethod = project.procurementMethod || '';
        formData.algorithmCategory = project.algorithmCategory || '';
        formData.id = project.id;
        
        // 可以根据需要添加更多属性
        
        // 根据项目类型自动切换标准表
        if (formData.projectType === '工程咨询') {
          activeTableType.value = 'engineering';
        } else {
          activeTableType.value = 'standard';
        }
      }
    };
    

    const handleTableTypeChange = (tabName) => {
      activeTableType.value = tabName;
    };
    
    // 添加标准表行
    const addStandardTableRow = () => {
      if (!standardTable.scene || !standardTable.firstCategory || !standardTable.productCode || standardTable.quantity <= 0) {
        ElMessage.warning('请完整填写标准表信息');
        return;
      }
      
      standardTableRows.value.push({
        ...JSON.parse(JSON.stringify(standardTable)),
        id: Date.now() // 临时ID
      });
      
      // 清空当前行数据，准备下一行输入
      standardTable.scene = '';
      standardTable.firstCategory = '';
      standardTable.secondCategory = '';
      standardTable.productCode = '';
      standardTable.quantity = 0;
      
      ElMessage.success('添加成功');
    };
    
    // 添加工程咨询类标准表行
    const addEngineeringTableRow = () => {
      if (!engineeringTable.category || !engineeringTable.workResult) {
        ElMessage.warning('请至少填写类别和工作成果');
        return;
      }
      
      engineeringTableRows.value.push({
        ...JSON.parse(JSON.stringify(engineeringTable)),
        id: Date.now() // 临时ID
      });
      
      // 清空部分字段，保留一些常用值
      engineeringTable.workResult = '';
      engineeringTable.difficulty = '';
      engineeringTable.staffing = '';
      engineeringTable.evaluation = '';
      engineeringTable.budgetLimit = '';
      
      ElMessage.success('添加成功');
    };

    // 选择计算方式
    const selectCalculationMethod = (method) => {
      formData.calculationMethod = method;
      ElMessage.success(`已选择${getCalculationMethodName(method)}计算方式`);
    };
    
    // 获取计算方式的中文名称
    const getCalculationMethodName = (method) => {
      const methodNames = {
        'government': '政府、行业指导价格法',
        'cost': '成本核算法',
        'historical': '历史价格法',
        'market': '市场调研法',
        'comprehensive': '综合定价法'
      };
      return methodNames[method] || '未选择';
    };

    // 验证培训类标准表是否完整填写
    const validateTrainingTable = () => {
      if (trainingTableRows.value.length === 0) {
        ElMessage.warning('请至少添加一条培训类标准表数据');
        return false;
      }
      
      for (let i = 0; i < trainingTableRows.value.length; i++) {
        const row = trainingTableRows.value[i];
        if (!row.trainingLocation) {
          ElMessage.warning(`第${i+1}行培训类标准表的培训场景不能为空`);
          return false;
        }
        if (!row.teacherTitle) {
          ElMessage.warning(`第${i+1}行培训类标准表的师资职称不能为空`);
          return false;
        }
        if (!row.hoursPerDay || row.hoursPerDay <= 0) {
          ElMessage.warning(`第${i+1}行培训类标准表的每天学时必须大于0`);
          return false;
        }
        if (!row.trainingDays || row.trainingDays <= 0) {
          ElMessage.warning(`第${i+1}行培训类标准表的培训天数必须大于0`);
          return false;
        }
        if (row.trainingPeople === undefined || row.trainingPeople === null || row.trainingPeople < 0) {
          ElMessage.warning(`第${i+1}行培训类标准表的培训人数不能为空且不能小于0`);
          return false;
        }
      }
      
      return true;
    };

    // 验证工程咨询类标准表是否完整填写
    const validateEngineeringTable = () => {
      if (engineeringTableRows.value.length === 0) {
        ElMessage.warning('请至少添加一条工程咨询类标准表数据');
        return false;
      }
      
      for (let i = 0; i < engineeringTableRows.value.length; i++) {
        const row = engineeringTableRows.value[i];
        if (!row.category) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的工程咨询类别不能为空`);
          return false;
        }
        if (!row.workResult) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的编制完成工作成果不能为空`);
          return false;
        }
        if (!row.difficulty) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的编制技术难度不能为空`);
          return false;
        }
        if (!row.staffing) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的工程人员配置情况不能为空`);
          return false;
        }
        if (!row.evaluation) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的评估值不能为空`);
          return false;
        }
        if (!row.adjustmentFactor) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的调整系数不能为空`);
          return false;
        }
        if (!row.facilityScale) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的设施规模不能为空`);
          return false;
        }
        if (!row.designDepth) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的设计深度不能为空`);
          return false;
        }
        if (!row.complexityFactor) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的专业复杂系数不能为空`);
          return false;
        }
        if (!row.engineeringComplexity) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的工程复杂程度影响系数不能为空`);
          return false;
        }
      }
      
      return true;
    };

    // 验证京东慧采标准表是否完整填写
    const validateJdTable = () => {
      if (jdTableRows.value.length === 0) {
        ElMessage.warning('请至少添加一条京东慧采标准表数据');
        return false;
      }
      
      for (let i = 0; i < jdTableRows.value.length; i++) {
        const row = jdTableRows.value[i];
        if (!row.feeName) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的费用名称不能为空`);
          return false;
        }
        if (!row.firstCategory) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的一级分类不能为空`);
          return false;
        }
        if (!row.secondCategory) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的二级分类不能为空`);
          return false;
        }
        if (!row.attributes || row.attributes.length === 0) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的商品属性不能为空`);
          return false;
        }
        if (!row.quantity || row.quantity <= 0) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的数量必须大于0`);
          return false;
        }
      }
      
      return true;
    };

    const nextStep = () => {
      if (activeStep.value === 2 && !formData.calculationMethod) {
        ElMessage.warning('请选择一种计算方式');
        return;
      }
      
      if (activeStep.value < 4) {
        activeStep.value += 1;
      }
    };

    const prevStep = () => {
      if (activeStep.value > 1) {
        activeStep.value -= 1;
      }
    };

    const goBack = () => {
      // 返回上一页或首页
      history.back();
    };

    const finishProcess = () => {
      ElMessage.success('项目最高限价编制完成！');
      // 可以跳转到其他页面或重置流程
      activeStep.value = 1;
    };

    // 步骤导航方法
    const navigateToStep = (stepNumber) => {
      // 只允许导航到已完成的步骤或当前步骤
      if (stepNumber <= activeStep.value) {
        activeStep.value = stepNumber;
      } else {
        ElMessage.warning('请按顺序完成步骤');
      }
    };

    onMounted(async () => {
      // 检查URL参数
      const { projectId, step, mode } = route.query;
      
      if (projectId) {
        // 设置选中的项目ID
        formData.id = projectId;
        formData.selectedProject = projectId;
        
        // 设置详情模式标志
        if (mode === 'detail') {
          isDetailMode.value = true;
          
          try {
            // 使用 getDetail 接口获取项目详情
            const projectRes = await getDetail(projectId);
            if (projectRes.data && projectRes.data.success) {
              const projectData = projectRes.data.data;
              
              // 将项目对象的属性赋值给formData
              formData.projectName = projectData.name;
              formData.projectCode = projectData.code || `P${projectData.id}`;
              formData.projectType = projectData.type || '';
              formData.projectCategory = projectData.category || '';
              formData.projectDescription = projectData.content || '';
              formData.procurementMethod = projectData.procurementMethod || '';
              formData.algorithmCategory = projectData.algorithmCategory || '';
              formData.id = projectData.id;
              
              // 获取项目报告详情
              const reportRes = await getProjectReport(projectId);
              if (reportRes.data && reportRes.data.success) {
                formData.reportData = reportRes.data.data;
                
                // 如果报告内容是JSON字符串，则解析它
                if (formData.reportData && formData.reportData.reportContent) {
                  try {
                    const reportContent = JSON.parse(formData.reportData.reportContent);
                    
                    // 将报告内容中的数据合并到formData中
                    if (reportContent.formData) {
                      // 合并计算方法
                      formData.calculationMethod = reportContent.formData.calculationMethod || formData.calculationMethod;
                      
                      // 合并计算结果
                      formData.calculationResult = reportContent.formData.calculationResult || null;
                      
                      // 合并标准表数据
                      if (formData.algorithmCategory === '培训类' && reportContent.formData.trainingTableRows) {
                        formData.trainingTableRows = reportContent.formData.trainingTableRows;
                      } else if (formData.algorithmCategory === '工程咨询类' && reportContent.formData.engineeringTableRows) {
                        formData.engineeringTableRows = reportContent.formData.engineeringTableRows;
                      } else if (reportContent.formData.standardTableRows) {
                        formData.standardTableRows = reportContent.formData.standardTableRows;
                      }
                    }
                  } catch (error) {
                    console.error('解析报告内容失败:', error);
                  }
                }
              }
            }
          } catch (error) {
            console.error('获取项目详情失败:', error);
            ElMessage.error('获取项目详情失败: ' + (error.message || '未知错误'));
          }
        } else {
          // 非详情模式，使用原有的处理方式
          handleProjectChange(projectId);
        }
        
        // 如果指定了步骤，直接跳转到该步骤
        if (step && !isNaN(parseInt(step))) {
          activeStep.value = parseInt(step);
        }
      }
      
      // 根据当前算法类型初始化表格
      if (formData.algorithmCategory === '培训类') {
        if (!isDetailMode.value) { // 只在非详情模式下初始化
          initTrainingTableRows();
        }
        fetchTrainingCategories();
      } else if (formData.algorithmCategory === '工程咨询类') {
        // 初始化工程咨询类表格，只在非详情模式下初始化
      } else {
        if (!isDetailMode.value) { // 只在非详情模式下初始化
          addJdTableRow();
        }
        fetchJdCategories();
      }
    });

    // 京东慧采分类数据
    const jdCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: []
    });
    
    // 京东慧采表格行数据
    const jdTableRows = ref([
      {
        feeName: '',
        firstCategory: '',
        secondCategory: '',
        attributes: [],
        attributeGroups: [],
        quantity: 1
      }
    ]);
    
    // 获取京东慧采分类数据
    const fetchJdCategories = async () => {
      try {
        const res = await getJdCategories();
        if (res.data.success) {
          const data = res.data.data;
          jdCategories.feeNames = data.feeNames || [];
          jdCategories.firstCategories = data.firstCategories || [];
          jdCategories.secondCategories = data.secondCategories || [];
        }
      } catch (error) {
        console.error('获取京东慧采分类数据失败:', error);
      }
    };
    
    // 处理费用名称变更
    const handleFeeNameChange = async (row) => {
      if (!row.feeName) {
        row.attributeGroups = [];
        row.attributes = [];
        return;
      }
      
      try {
        const res = await getProductAttributes(row.feeName);
        if (res.data.success) {
          row.attributeGroups = res.data.data || [];
          row.attributes = []; // 清空已选属性
        }
      } catch (error) {
        console.error('获取商品属性失败:', error);
      }
    };
    
    // 添加京东慧采表格行
    const addJdTableRow = () => {
      jdTableRows.value.push({
        feeName: '',
        firstCategory: '',
        secondCategory: '',
        attributes: [],
        attributeGroups: [],
        quantity: 1
      });
    };
    
    // 删除京东慧采表格行
    const removeJdTableRow = (index) => {
      jdTableRows.value.splice(index, 1);
    };
    
    // 培训类相关的数据和方法
    const trainingTableRows = ref([]);
    const trainingCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: [],
      teacherTitles: []
    });

    // 初始化培训类表格行
    const initTrainingTableRows = () => {
      trainingTableRows.value = [{
        trainingLocation: '线下',
        teacherTitle: '',
        hoursPerDay: 8,
        trainingDays: 1,
        trainingPeople: 0
      }];
    };

    // 添加培训类表格行
    const addTrainingTableRow = () => {
      trainingTableRows.value.push({
        trainingLocation: '线下',
        teacherTitle: '',
        hoursPerDay: 8,
        trainingDays: 1,
        trainingPeople: 0
      });
    };

    // 删除培训类表格行
    const removeTrainingTableRow = (index) => {
      trainingTableRows.value.splice(index, 1);
    };

    // 处理培训类费用名称变更
    const handleTrainingFeeNameChange = async (row) => {
      if (!row.feeName) {
        return;
      }
      
      try {
        // 根据选择的费用名称获取相关数据
        const res = await getTrainingAttributes(row.feeName);
        if (res.data.success) {
          // 自动填充相关字段
          const data = res.data.data;
          if (data) {
            // 设置师资职称
            if (data.teacherTitle) {
              row.teacherTitle = data.teacherTitle;
            }
            
            // 设置一级分类
            if (data.firstCategory) {
              row.firstCategory = data.firstCategory;
            }
            
            // 设置二级分类
            if (data.secondCategory) {
              row.secondCategory = data.secondCategory;
            }
          }
        }
      } catch (error) {
        console.error('获取培训属性失败:', error);
      }
    };

    // 获取培训类分类数据
    const fetchTrainingCategories = async () => {
      try {
        const res = await getTrainingCategories();
        if (res.data.success) {
          trainingCategories.feeNames = res.data.data.feeNames || [];
          trainingCategories.firstCategories = res.data.data.firstCategories || [];
          trainingCategories.secondCategories = res.data.data.secondCategories || [];
          trainingCategories.teacherTitles = res.data.data.teacherTitles || [];
        }
      } catch (error) {
        console.error('获取培训类分类数据失败:', error);
      }
    };

    // 监听项目选择变化，初始化对应的表格
    watch(() => formData.algorithmCategory, (newVal) => {
      if (newVal === '培训类') {
        initTrainingTableRows();
        fetchTrainingCategories();
      } else if (newVal === '工程咨询类') {
        // 初始化工程咨询类表格
      } else {
        // 初始化京东慧采表格
        if (jdTableRows.value.length === 0) {
          addJdTableRow();
        }
        fetchJdCategories();
      }
    });

    onMounted(() => {
      // 根据当前算法类型初始化表格
      if (formData.algorithmCategory === '培训类') {
        initTrainingTableRows();
        fetchTrainingCategories();
      } else if (formData.algorithmCategory === '工程咨询类') {
        // 初始化工程咨询类表格
      } else {
        addJdTableRow();
        fetchJdCategories();
      }
    });

    return {
      activeStep,
      formData,
      isDetailMode, // 导出详情模式标志
      steps,
      selectCalculationMethod,
      getCalculationMethodName,
      nextStep,
      prevStep,
      goBack,
      finishProcess,
      navigateToStep
    };
  }
}
</script>

<style lang="scss" scoped>
.project-assistant-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 120px);
  position: relative;
}

/* 优化后的头部样式 */
.assistant-header {
  margin-bottom: 40px;
  text-align: center;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .header-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  }

  .header-text {
    flex: 1;
    text-align: left;
  }

  .main-title {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .subtitle {
    color: #64748b;
    font-size: 16px;
    margin: 0 0 16px 0;
    font-weight: 400;
  }

  .feature-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .tag {
      padding: 4px 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
    }
  }
}

/* 主要内容布局 */
.main-content-layout {
  display: flex;
  gap: 24px;
  min-height: 600px;
}

/* 左侧步骤导航 */
.steps-sidebar {
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  position: sticky;
  top: 24px;
  height: fit-content;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.sidebar-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #64748b;

    .current-step {
      font-weight: 600;
      color: #667eea;
    }
  }
}

/* 步骤导航项 */
.steps-navigation {
  flex: 1;
  margin-bottom: 24px;
}

.step-nav-item {
  display: flex;
  align-items: center;
  padding: 16px 12px;
  margin-bottom: 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);

    .step-nav-icon {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .step-nav-title {
      color: white;
      font-weight: 600;
    }

    .step-nav-description {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  &.completed {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;

    .step-nav-icon {
      background: #10b981;
      color: white;
    }

    .step-nav-title {
      color: #059669;
      font-weight: 600;
    }

    .step-nav-description {
      color: #065f46;
    }

    &:hover {
      background: #ecfdf5;
      border-color: #86efac;
    }
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;

    .step-nav-icon {
      background: #f1f5f9;
      color: #94a3b8;
    }

    .step-nav-title {
      color: #94a3b8;
    }

    .step-nav-description {
      color: #cbd5e1;
    }
  }

  &:not(.disabled):not(.active):hover {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
  }
}

.step-nav-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  color: #64748b;
  margin-right: 12px;
  transition: all 0.3s ease;
  flex-shrink: 0;

  .step-number {
    font-weight: 600;
    font-size: 14px;
  }

  .completed-icon,
  .active-icon {
    font-size: 18px;
  }
}

.step-nav-content {
  flex: 1;
  min-width: 0;

  .step-nav-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 2px;
    line-height: 1.4;
  }

  .step-nav-description {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.3;
  }
}

.step-nav-status {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .status-icon {
    font-size: 16px;

    &.completed {
      color: #10b981;
    }

    &.active {
      color: white;
      animation: spin 2s linear infinite;
    }
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &.pending {
      background: #cbd5e1;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 左侧进度条 */
.sidebar-progress {
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;

  .progress-label {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 12px;
    font-weight: 500;
  }

  .progress-bar-container {
    display: flex;
    align-items: center;
    gap: 12px;

    .progress-bar {
      flex: 1;
      height: 60px;
      background: #f1f5f9;
      border-radius: 6px;
      position: relative;
      overflow: hidden;

      .progress-fill {
        width: 100%;
        background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
        border-radius: 6px;
        transition: height 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: absolute;
        bottom: 0;
      }
    }

    .progress-percentage {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      min-width: 35px;
    }
  }
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  min-width: 0;
}

.step-content-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 500px;
  position: relative;
  overflow: hidden;
}

.step-wrapper {
  width: 100%;
}

/* 过渡动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}



/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content-layout {
    flex-direction: column;
    gap: 16px;
  }

  .steps-sidebar {
    width: 100%;
    position: static;
    max-height: none;

    .steps-navigation {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin-bottom: 16px;
    }

    .step-nav-item {
      margin-bottom: 0;
    }

    .sidebar-progress {
      .progress-bar-container {
        .progress-bar {
          height: 8px;

          .progress-fill {
            width: var(--progress-width, 0%);
            height: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .project-assistant-container {
    padding: 16px;
  }

  .assistant-header .header-content {
    flex-direction: column;
    text-align: center;
    padding: 20px;

    .header-text {
      text-align: center;
    }

    .main-title {
      font-size: 24px;
    }

    .feature-tags {
      justify-content: center;
    }
  }

  .steps-sidebar {
    padding: 16px;

    .steps-navigation {
      grid-template-columns: 1fr;
    }

    .step-nav-item {
      padding: 12px;

      .step-nav-icon {
        width: 32px;
        height: 32px;
        margin-right: 8px;
      }

      .step-nav-content {
        .step-nav-title {
          font-size: 13px;
        }

        .step-nav-description {
          font-size: 11px;
        }
      }
    }
  }

  .step-content-container {
    padding: 20px;
    min-height: 400px;
  }
}

/* 全局样式优化 */
:deep(.el-steps) {
  .el-step__head {
    .el-step__icon {
      border: 2px solid #e5e7eb;
      background: white;
      transition: all 0.3s ease;

      &.is-process {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      &.is-finish {
        border-color: #10b981;
        background: #10b981;
        color: white;
      }
    }
  }

  .el-step__title {
    font-weight: 600;
    color: #374151;

    &.is-process {
      color: #667eea;
    }

    &.is-finish {
      color: #10b981;
    }
  }

  .el-step__description {
    color: #6b7280;
    font-size: 13px;
  }

  .el-step__line {
    background: #e5e7eb;

    &.is-finish {
      background: #10b981;
    }
  }
}

.step-content {
  padding: 10px;
}

.step-content h3 {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.step-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 25px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.half-width {
  flex: 1;
}

.full-width {
  width: 100%;
}

/* 标准表样式 */
.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #409EFF;
}

.standard-table-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-selector {
  padding: 15px;
}

.standard-table, .engineering-table {
  width: 100%;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 10px 0;
  font-weight: 500;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
}

.header-item {
  flex: 1;
  text-align: center;
  padding: 0 5px;
}

.table-row {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.row-item {
  flex: 1;
  padding: 0 5px;
}

.add-row {
  padding: 10px 0;
  text-align: center;
}

.full-width {
  width: 100%;
}

/* 工程咨询类标准表样式 */
.engineering-table .table-header,
.engineering-table .table-row {
  display: grid;
  grid-template-columns: repeat(11, 1fr);
  gap: 5px;
}

.engineering-table .header-item,
.engineering-table .row-item {
  padding: 5px;
  text-align: center;
  font-size: 12px;
}

/* 计算方式选择样式 */
.calculation-methods-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.calculation-method-row {
  display: flex;
  gap: 20px;
}

.calculation-method-card {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
}

.calculation-method-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.calculation-method-card.selected {
  border: 2px solid #409eff;
  background-color: #ecf5ff;
}

.method-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.method-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409eff;
}

.method-header h4 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #303133;
}

.method-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 15px;
  line-height: 1.4;
}

.method-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.feature-item .el-icon {
  color: #67c23a;
  margin-right: 5px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.button-container .el-button {
  min-width: 100px;
}

.standard-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 10px;
  font-weight: bold;
}

.table-row {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ebeef5;
}

.header-item, .row-item {
  flex: 1;
  padding: 0 5px;
}

.full-width {
  width: 100%;
}

.add-row {
  padding: 10px;
  text-align: center;
  border-top: 1px solid #ebeef5;
}

/* 培训类标准表样式 */
.standard-table {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 20px;
  
  .table-header {
    display: flex;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    
    .header-item {
      flex: 1;
      padding: 12px 8px;
      text-align: center;
      font-weight: bold;
      color: #606266;
    }
  }
  
  .table-row {
    display: flex;
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .row-item {
      flex: 1;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .add-row {
    padding: 10px;
    text-align: center;
    border-top: 1px solid #ebeef5;
  }
}
</style>





