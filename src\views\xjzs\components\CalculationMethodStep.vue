<template>
  <div class="step-content">
    <div class="step-header">
      <div class="step-icon">
        <el-icon size="24"><Setting /></el-icon>
      </div>
      <div class="step-info">
        <h3>选择计算方式</h3>
        <p class="step-description">根据项目类型自动选择最适合的计算方式</p>
      </div>
    </div>
    
    <div class="calculation-methods-container">
      <!-- 只读状态提示 -->
      <div class="readonly-notice">
        <el-icon><InfoFilled /></el-icon>
        <span>计算方式已根据项目类型自动选择，无需手动选择</span>
      </div>

      <!-- 选中的计算方式卡片 -->
      <div class="selected-method-container">
        <div class="selected-method-card" v-if="formData.calculationMethod">
          <div class="method-header">
            <el-icon class="method-icon">
              <component :is="getMethodIcon(formData.calculationMethod)" />
            </el-icon>
            <h4>{{ getCalculationMethodName(formData.calculationMethod) }}</h4>
            <div class="selected-badge">
              <el-icon><Check /></el-icon>
              <span>已选择</span>
            </div>
          </div>
          <p class="method-description">{{ getMethodDescription(formData.calculationMethod) }}</p>
          <div class="method-features">
            <div
              class="feature-item"
              v-for="feature in getMethodFeatures(formData.calculationMethod)"
              :key="feature">
              <el-icon><Check /></el-icon>
              {{ feature }}
            </div>
          </div>
        </div>
      </div>

      <!-- 展开/收起按钮 -->
      <div class="expand-toggle">
        <el-button
          type="text"
          @click="toggleExpanded"
          class="toggle-button">
          <el-icon><component :is="isExpanded ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
          {{ isExpanded ? '收起其他选项' : '查看其他选项' }}
        </el-button>
      </div>

      <!-- 其他计算方式（可展开/收起） -->
      <div class="other-methods-container" v-show="isExpanded">
        <div class="other-methods-title">
          <h5>其他计算方式</h5>
          <p>以下为其他可选的计算方式，当前已自动选择最适合的方式</p>
        </div>

        <div class="calculation-method-row">
          <!-- 历史价格法 -->
          <div
            class="calculation-method-card readonly collapsed"
            :class="{ 'selected': formData.calculationMethod === 'historical' }"
            v-if="formData.calculationMethod !== 'historical'">
            <div class="readonly-overlay"></div>
            <div class="method-header">
              <el-icon class="method-icon"><Clock /></el-icon>
              <h4>历史价格法</h4>
            </div>
            <p class="method-description">基于历史采购数据进行分析计算的价格推荐</p>
            <div class="method-features">
              <div class="feature-item"><el-icon><Check /></el-icon> 数据覆盖面广</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 计算结果准确</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 适合常规采购</div>
            </div>
          </div>

          <!-- 市场调研法 -->
          <div
            class="calculation-method-card readonly collapsed"
            :class="{ 'selected': formData.calculationMethod === 'market' }"
            v-if="formData.calculationMethod !== 'market'">
            <div class="readonly-overlay"></div>
            <div class="method-header">
              <el-icon class="method-icon"><TrendCharts /></el-icon>
              <h4>市场调研法</h4>
            </div>
            <p class="method-description">基于市场调研数据进行分析计算的价格推荐</p>
            <div class="method-features">
              <div class="feature-item"><el-icon><Check /></el-icon> 价格与市场同步</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 反映最新市场行情</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 适合市场波动大的产品</div>
            </div>
          </div>
        </div>

        <div class="calculation-method-row">
          <!-- 成本核算法 -->
          <div
            class="calculation-method-card readonly collapsed"
            :class="{ 'selected': formData.calculationMethod === 'cost' }"
            v-if="formData.calculationMethod !== 'cost'">
            <div class="readonly-overlay"></div>
            <div class="method-header">
              <el-icon class="method-icon"><Document /></el-icon>
              <h4>成本核算法</h4>
            </div>
            <p class="method-description">基于产品成本结构进行分析计算的价格，更加客观可靠</p>
            <div class="method-features">
              <div class="feature-item"><el-icon><Check /></el-icon> 成本分析清晰</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 利润空间可控</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 适合特殊产品采购</div>
            </div>
          </div>

          <!-- 政府、行业指导价格法 -->
          <div
            class="calculation-method-card readonly collapsed"
            :class="{ 'selected': formData.calculationMethod === 'government' }"
            v-if="formData.calculationMethod !== 'government'">
            <div class="readonly-overlay"></div>
            <div class="method-header">
              <el-icon class="method-icon"><User /></el-icon>
              <h4>政府、行业指导价格法</h4>
            </div>
            <p class="method-description">基于政府或行业发布的指导价格进行计算的价格推荐</p>
            <div class="method-features">
              <div class="feature-item"><el-icon><Check /></el-icon> 符合政府规定，政策合规</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 避免审计风险和合规性问题</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 适用于受监管的产品或服务</div>
            </div>
          </div>
        </div>

        <!-- 综合定价法 -->
        <div class="calculation-method-row">
          <div
            class="calculation-method-card readonly collapsed"
            :class="{ 'selected': formData.calculationMethod === 'comprehensive' }"
            v-if="formData.calculationMethod !== 'comprehensive'">
            <div class="readonly-overlay"></div>
            <div class="method-header">
              <el-icon class="method-icon"><SetUp /></el-icon>
              <h4>综合定价法</h4>
            </div>
            <p class="method-description">综合历史价格、市场调研、成本核算等多种因素进行综合定价</p>
            <div class="method-features">
              <div class="feature-item"><el-icon><Check /></el-icon> 多方面的价格参考</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 分析更加全面</div>
              <div class="feature-item"><el-icon><Check /></el-icon> 适合复杂产品的采购</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="button-container">
      <el-button @click="prevStep">返回</el-button>
      <el-button type="primary" @click="nextStep">下一步：引擎计算</el-button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Check, Clock, TrendCharts, Document, SetUp, User, Delete, Plus, InfoFilled, Setting, ArrowUp, ArrowDown } from '@element-plus/icons-vue';

export default {
  name: 'CalculationMethodStep',
  components: {
    Check,
    Clock,
    TrendCharts,
    Document,
    SetUp,
    User,
    Delete,
    Plus,
    InfoFilled,
    Setting,
    ArrowUp,
    ArrowDown
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['next-step', 'prev-step'],
  setup(props, { emit }) {
    // 展开/收起状态
    const isExpanded = ref(false);

    // 切换展开/收起状态
    const toggleExpanded = () => {
      isExpanded.value = !isExpanded.value;
    };

    // 根据算法类型设置默认计算方式
    const setDefaultCalculationMethod = () => {
      const algorithmCategory = props.formData.algorithmCategory;

      if (algorithmCategory === '培训类' || algorithmCategory === '工程咨询类') {
        props.formData.calculationMethod = 'government';
        ElMessage.success(`已根据项目类型"${algorithmCategory}"自动选择"政府、行业指导价格法"`);
      } else {
        props.formData.calculationMethod = 'cost';
        ElMessage.success(`已根据项目类型自动选择"成本核算法"`);
      }
    };
    
    // 监听算法类型变化，自动设置默认计算方式
    watch(() => props.formData.algorithmCategory, (newVal) => {
      if (newVal) {
        setDefaultCalculationMethod();
      }
    }, { immediate: true });
    
    // 组件挂载时设置默认计算方式
    onMounted(() => {
      if (props.formData.algorithmCategory && !props.formData.calculationMethod) {
        setDefaultCalculationMethod();
      }
    });
    
    // 获取计算方式的中文名称
    const getCalculationMethodName = (method) => {
      const methodNames = {
        'government': '政府、行业指导价格法',
        'cost': '成本核算法',
        'historical': '历史价格法',
        'market': '市场调研法',
        'comprehensive': '综合定价法'
      };
      return methodNames[method] || '未选择';
    };

    // 获取计算方式的图标
    const getMethodIcon = (method) => {
      const methodIcons = {
        'government': 'User',
        'cost': 'Document',
        'historical': 'Clock',
        'market': 'TrendCharts',
        'comprehensive': 'SetUp'
      };
      return methodIcons[method] || 'Setting';
    };

    // 获取计算方式的描述
    const getMethodDescription = (method) => {
      const methodDescriptions = {
        'government': '基于政府或行业发布的指导价格进行计算的价格推荐',
        'cost': '基于产品成本结构进行分析计算的价格，更加客观可靠',
        'historical': '基于历史采购数据进行分析计算的价格推荐',
        'market': '基于市场调研数据进行分析计算的价格推荐',
        'comprehensive': '综合历史价格、市场调研、成本核算等多种因素进行综合定价'
      };
      return methodDescriptions[method] || '';
    };

    // 获取计算方式的特性
    const getMethodFeatures = (method) => {
      const methodFeatures = {
        'government': [
          '符合政府规定，政策合规',
          '避免审计风险和合规性问题',
          '适用于受监管的产品或服务'
        ],
        'cost': [
          '成本分析清晰',
          '利润空间可控',
          '适合特殊产品采购'
        ],
        'historical': [
          '数据覆盖面广',
          '计算结果准确',
          '适合常规采购'
        ],
        'market': [
          '价格与市场同步',
          '反映最新市场行情',
          '适合市场波动大的产品'
        ],
        'comprehensive': [
          '多方面的价格参考',
          '分析更加全面',
          '适合复杂产品的采购'
        ]
      };
      return methodFeatures[method] || [];
    };

    const nextStep = () => {
      if (!props.formData.calculationMethod) {
        ElMessage.warning('计算方式未设置，请检查项目类型');
        return;
      }
      
      emit('next-step');
    };

    const prevStep = () => {
      emit('prev-step');
    };

    return {
      isExpanded,
      toggleExpanded,
      getCalculationMethodName,
      getMethodIcon,
      getMethodDescription,
      getMethodFeatures,
      nextStep,
      prevStep
    };
  }
}
</script>

<style lang="scss" scoped>
.step-content {
  padding: 10px;
}

.step-content h3 {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.step-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 25px;
}

.calculation-methods-container {
  margin-bottom: 30px;
}

.calculation-method-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.calculation-method-card {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
  position: relative;
  overflow: hidden;
}

.calculation-method-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.calculation-method-card.selected {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.method-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.method-icon {
  font-size: 24px;
  color: #409EFF;
  margin-right: 10px;
}

.method-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.method-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 15px;
  min-height: 40px;
}

.method-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 13px;
}

.feature-item .el-icon {
  color: #67c23a;
  margin-right: 5px;
}

.button-container {
  display: flex;
  justify-content: space-between;
}

.button-container .el-button {
  min-width: 100px;
}

/* 培训类标准表样式 */
.training-table-container {
  margin-bottom: 30px;
}

.training-table-container h4 {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.training-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 12px 0;
  font-weight: bold;
}

.header-item {
  flex: 1;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.table-row {
  display: flex;
  border-top: 1px solid #ebeef5;
  padding: 12px 0;
  align-items: center;
}

.row-item {
  flex: 1;
  padding: 0 10px;
  text-align: center;
}

.row-action {
  width: 50px;
  display: flex;
  justify-content: center;
}

.add-row {
  padding: 12px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.full-width {
  width: 100%;
}

.button-container {
  display: flex;
  justify-content: space-between;
}

.button-container .el-button {
  min-width: 100px;
}

/* 只读状态提示 */
.readonly-notice {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  margin-bottom: 20px;
  color: #409EFF;
  font-size: 14px;
}

.readonly-notice .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 只读状态的计算方式卡片 */
.calculation-method-card.readonly {
  cursor: not-allowed;
  position: relative;
}

.calculation-method-card.readonly:hover {
  transform: none;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
}

/* 禁用叠加层 */
.readonly-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
  border-radius: 4px;
}

/* 选中状态的卡片保持高亮 */
.calculation-method-card.readonly.selected {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.calculation-method-card.readonly.selected .readonly-overlay {
  display: none;
}

/* 选中的计算方式卡片样式 */
.selected-method-container {
  margin-bottom: 20px;
}

.selected-method-card {
  border: 2px solid #409EFF;
  border-radius: 8px;
  padding: 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
  position: relative;
  overflow: hidden;
}

.selected-method-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409EFF 0%, #67c23a 100%);
}

.selected-method-card .method-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.selected-method-card .method-icon {
  font-size: 28px;
  color: #409EFF;
  margin-right: 12px;
}

.selected-method-card .method-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  flex: 1;
}

.selected-badge {
  display: flex;
  align-items: center;
  background: #67c23a;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.selected-badge .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.selected-method-card .method-description {
  color: #475569;
  font-size: 15px;
  margin-bottom: 16px;
  line-height: 1.6;
}

.selected-method-card .method-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.selected-method-card .feature-item {
  display: flex;
  align-items: center;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.selected-method-card .feature-item .el-icon {
  color: #67c23a;
  margin-right: 8px;
  font-size: 16px;
}

/* 展开/收起按钮样式 */
.expand-toggle {
  text-align: center;
  margin: 20px 0;
}

.toggle-button {
  color: #409EFF;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.toggle-button:hover {
  background-color: #f0f9ff;
  color: #1e40af;
}

.toggle-button .el-icon {
  margin-right: 6px;
  transition: transform 0.3s ease;
}

/* 其他方式容器样式 */
.other-methods-container {
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
  margin-top: 20px;
}

.other-methods-title {
  margin-bottom: 20px;
  text-align: center;
}

.other-methods-title h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.other-methods-title p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
}

/* 收起状态的卡片样式 */
.calculation-method-card.collapsed {
  opacity: 0.7;
  transform: scale(0.98);
}

.calculation-method-card.collapsed:hover {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 展开/收起动画 */
.other-methods-container {
  transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selected-method-card .method-features {
    grid-template-columns: 1fr;
  }

  .calculation-method-row {
    flex-direction: column;
  }

  .selected-method-card {
    padding: 20px;
  }

  .selected-method-card .method-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .selected-badge {
    align-self: flex-end;
  }
}
</style>






